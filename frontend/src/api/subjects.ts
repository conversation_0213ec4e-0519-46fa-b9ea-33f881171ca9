import http from '@/utils/http';

// 专题-列表
export const getSubjectsListApi = async (params: any): Promise<any> => {
    return await http.get('/subjects/', { params });
}


// 专题-添加
export const addSubjectApi = async (title: string): Promise<any> => {
    return await http.post('/subject/add/', { title });
}

// 专题-排序
export const sortSubjectsApi = async (li_subjects_id: string[]): Promise<any> => {
    return await http.post('/subjects/sort/', { li_subjects_id });
}

// 专题-详情
export const getSubjectDetailsApi = async (subject_id: number | string, no_cache: number = 0): Promise<any> => {
    return await http.get(`/subject/${subject_id}/`, { params: { no_cache } });
}

// 专题-详情-书本-排序
export const sortSubjectsBooksApi = async (subject_id: number | string, li_book_id: []) => {
    return await http.post(`/subject/${subject_id}/books/sort/`, { li_book_id })
}


// 专题-书本-删除
export const delSubjectBookApi = async (subject_id: number | string, book_id: number | string) => {
    return await http.post(`/subject/${subject_id}/${book_id}/del/`)
}