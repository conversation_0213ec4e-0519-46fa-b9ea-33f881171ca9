import type { Genre, Subject, Tag } from '@/types/book'
import { ref } from 'vue'


export const useBookMetaStore = defineStore('bookMeta', () => {
  // 状态
  const genres = ref<Genre[]>([])
  const tags = ref<Tag[]>([])
  const subjects = ref<Subject[]>([])
  const isGenresInitialized = ref(false)
  const isTagsInitialized = ref(false)
  const isSubjectsInitialized = ref(false)

  // 获取所有元数据（种类和标签）
  const fetchAllMeta = async () => {
    const [resGenre, resTags, resSubjects] = await Promise.all([
      fetchGenres(),
      fetchTags(),
      fetchSubjects(),
    ])

    return {
      genres: resGenre,
      tags: resTags,
      subjects: resSubjects
    }
  }

  /**
   * 获取类型
   * @param refresh 是否刷新缓存
   * @returns 
   */
  const fetchGenres = async (refresh: boolean = false) => {
    // 非强制刷新缓存 && 已缓存，则直接返回返还
    if (!refresh && isGenresInitialized.value) return genres.value

    // 请求数据
    try {
      const data = await getGenresListApi()
      genres.value = data
      isGenresInitialized.value = true
      return genres.value
    } catch (error) {
      console.error('获取类型列表失败:', error)
      throw error
    }
  }

  // 仅获取标签
  const fetchTags = async (refresh: boolean = false) => {
    // 非强制刷新缓存 && 已缓存，则直接返回返还
    if (!refresh && isTagsInitialized.value) return tags.value

    try {
      const data = await getTagsListApi()
      tags.value = data
      isTagsInitialized.value = true  // 缓存
      return tags.value
    } catch (error) {
      console.error('获取标签列表失败:', error)
      throw error
    }
  }

  // 仅获取专题
  const fetchSubjects = async (refresh: boolean = false) => {
    // 非强制刷新缓存 && 已缓存，则直接返回返还
    if (!refresh && isSubjectsInitialized.value) return subjects.value

    try {
      const data = await getSubjectsListApi({ page: 1, size: 50 })
      subjects.value = data.list
      isSubjectsInitialized.value = true
      return subjects.value
    } catch (error) {
      console.error('获取专题列表失败:', error)
      throw error
    }
  }



  return {
    genres,
    tags,
    subjects,
    fetchAllMeta,
    fetchGenres,
    fetchTags,
    fetchSubjects,
  }
}) 