export interface Tag {
  id: number | string;
  value: string;
  book_count: number;
}

export interface Genre {
  id: number | string;
  value: string;
  book_count: number;
}

export interface Book {
  id: number | string;
  title: string;
  first_img: string;
  page_count: number;
  genre?: string;
  tags?: Tag[];
  is_collect?: boolean;
  asc_priority: number;
  li_subjects: [{ id: '', title: '', thumb_url: '' }]
}

export interface BookPage {
  id: number;
  img_path: string;
  order: number;
  page: number;
}


export interface Subject {
  id: number | string;
  title: string;
  thumb_url: string;
}