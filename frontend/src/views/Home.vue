<template>
  <main class="home-container">
    <div class="opr-block-container" :class="{ collapsed: isCollapse }" ref="oprBlockContainer">
      <!-- 操作栏 -->
      <div class="opr-block">
        <!-- 搜索框 -->
        <my-input v-model="key" placeholder="搜索..." clear class="key-search">
          <template #prefix>
            <font-awesome-icon icon="fa-solid fa-search"></font-awesome-icon>
          </template>
        </my-input>

        <!-- 类型-下拉框 -->
        <my-select
          v-model="form.genre"
          :options="
            bookMetaStore.genres.map((item: Genre) => ({
              label: `${item.value} (${item.book_count})`,
              value: item.value,
            }))
          "
          clear
          placeholder="类型"
          class="genre-select"
        >
          <template #prefix>
            <font-awesome-icon icon="fa-solid fa-filter" />
          </template>
        </my-select>

        <!-- 标签-下拉框 -->
        <filter-tags-selector v-model:liChoseTags="form.tags" :li-tags="bookMetaStore.tags" />

        <my-divider direction="vertical" class="opr-divider" />

        <!-- 无专题 -->
        <my-checkbox v-model="form.no_subject" :true-value="1" :false-value="0" label="无专题" />

        <!-- 无类型 -->
        <my-checkbox v-model="form.no_genre" :true-value="1" :false-value="0" label="无类型" />

        <!-- 无标签 -->
        <my-checkbox v-model="form.no_tags" :true-value="1" :false-value="0" label="无标签" />

        <!-- 我的收藏 -->
        <my-checkbox v-model="form.is_collect" :true-value="1" :false-value="0" border>
          <font-awesome-icon icon="fa-solid fa-star" />
          收藏
        </my-checkbox>

        <!-- 随机 -->
        <my-checkbox v-model="form.is_random" :true-value="1" :false-value="0" border>
          <font-awesome-icon icon="fa-solid fa-random" />
          随机
        </my-checkbox>

        <!-- 无图 -->
        <my-checkbox v-model="hideImg" label="无图" border />

        <my-divider direction="vertical" class="opr-divider" />

        <my-button type="danger" @click="reset" size="small" title="重置">
          <font-awesome-icon icon="fa-solid fa-undo" />
        </my-button>

        <my-button type="primary" @click="loadData" size="small" title="刷新">
          <font-awesome-icon icon="fa-solid fa-refresh" />
        </my-button>

        <div class="combine-block">
          <my-button
            type="success"
            @click="toggleCombineMode"
            size="small"
            title="合并"
            v-show="!combineMode"
          >
            <font-awesome-icon icon="fa-solid fa-object-group" />
          </my-button>
          <div v-show="combineMode">
            <my-button title="取消合并" size="small" @click="toggleCombineMode">
              <font-awesome-icon icon="fa-solid fa-xmark" />
            </my-button>
            <my-button
              type="success"
              title="准备合并排序"
              size="small"
              @click="openCombineSortDialog"
            >
              <font-awesome-icon icon="fa-solid fa-check" />
            </my-button>
          </div>
        </div>

        <!-- 显示总数 -->
        <div class="show-total-count">共 {{ totalCount }}</div>
      </div>
    </div>

    <!-- 折叠按钮 -->
    <my-button
      type="warning"
      @click="toggleCollapse"
      circle
      size="small"
      class="collapse-btn"
      :class="{ collapsed: isCollapse }"
    >
      <font-awesome-icon icon="fa-solid fa-angles-up" />
    </my-button>

    <!-- book列表 -->
    <div class="book-container" :style="{ marginTop: bookContainerMarginTop }">
      <div class="book-list">
        <book-card
          v-for="book in liBooks"
          :hide-img="hideImg"
          :key="book.id"
          :book="book"
          :no-jump="combineMode"
          class="book-item-in-home"
          :class="{
            combing: combineMode,
            active: isBookActive(book.id),
          }"
          @chose-genre="handleGenreClick"
          @chose-tag="handleTagClick"
          @ready-to-edit-tags="readyToEditTags"
          @ready-to-edit-genre="readyToEditGenre"
          @ready-to-edit-subjects="readyToEditSubjects"
          @refresh-book="loadData"
          @click="handleCombineBookClick(book)"
        />
      </div>

      <!-- 加载更多触发器 -->
      <div ref="loadMoreTrigger" class="load-more-trigger" v-if="!isEnd"></div>

      <!-- 没有更多数据提示 -->
      <my-divider v-if="isEnd && liBooks.length > 0" dashed>没有更多数据了</my-divider>
    </div>

    <!-- 弹窗 修改-类型 -->
    <edit-genre-dialog
      v-model:visible="editGenreVisible"
      :bookMeta="editGenreMeta"
      @on-close="() => (editGenreVisible = false)"
      @on-success="loadData"
    />

    <!-- 弹窗 修改-标签 -->
    <edit-tags-dialog
      v-model:visible="editTagsVisible"
      :bookMeta="editTagsMeta"
      @on-close="() => (editTagsVisible = false)"
      @on-success="loadData"
    />

    <!-- 弹窗 修改-专题 -->
    <edit-subjects-dialog
      v-model:visible="editSubjectsVisible"
      :bookMeta="editSubjectsMeta"
      @on-close="() => (editSubjectsVisible = false)"
      @on-success="loadData"
    />

    <!-- 弹窗 合并 -->
    <my-dialog v-model="combineDialogVisible" title="合并 书本" @close="closeCombineSortDialog">
      <div class="combine-block" ref="sortCombineRef">
        <div v-for="item in combineChosing" class="combine-block-item" :data-id="item.id">
          <img :src="item.first_img" alt="" />
          <span>{{ item.title }}</span>
        </div>
      </div>

      <template #footer>
        <div class="my_flex_end">
          <my-button @click="closeCombineSortDialog">取 消</my-button>
          <my-button type="primary" @click="submitToCombine">确 定</my-button>
        </div>
      </template>
    </my-dialog>
  </main>
</template>

<script setup lang="ts">
import type { Book, Genre, Subject, Tag } from '@/types/book'
import debounce from 'lodash/debounce'
import Sortable from 'sortablejs'

const PAGE_SIZE = 20

const route = useRoute()
const bookMetaStore = useBookMetaStore()
const liBooks = ref<Book[]>([])
const totalCount = ref<number>(0)
const key = ref<string>('')
const form = reactive({
  key: '',
  page: 1,
  size: PAGE_SIZE,
  genre: '',
  tags: [] as string[],
  no_genre: 0,
  no_tags: 0,
  no_subject: 0,
  is_collect: 0,
  is_random: 0,
})

// 下拉加载相关
const isLoadingMore = ref(false) // 是否正在加载更多
const isEnd = ref(false) // 是否 已经无数据了，达到末页

const isReset = ref<boolean>(false)
const hideImg = ref<boolean>(false) // 无图模式
const isCollapse = ref<boolean>(false) // 折叠模式

const combineMode = ref(false) // 合并模式
const combineChosing = ref<Book[]>([]) // 合并模式 选中的书本
const combineDialogVisible = ref(false) // 合并模式 编辑待合并书本的 排序
const combineSortable = ref<Sortable>() // 合并模式 排序对象
const sortCombineRef = ref<HTMLElement>()

// 操作栏容器的引用和高度
const oprBlockContainer = ref<HTMLElement | null>(null)
const oprBlockHeight = ref<number>(0) // 操作栏目 高度

/**
 * 切换 合并模式
 */
const toggleCombineMode = () => {
  combineMode.value = !combineMode.value

  if (!combineMode.value) {
    // 关闭，重置
    combineChosing.value = []
  }
}

/**
 * 准备 合并-排序
 */
const openCombineSortDialog = () => {
  combineDialogVisible.value = true

  // 初始化拖动
  nextTick(() => {
    if (sortCombineRef.value) {
      combineSortable.value = Sortable.create(sortCombineRef.value, {
        animation: 150,
      })
    }
  })
}

/**
 * 退出 合并-模式
 */
const closeCombineMode = () => {
  combineMode.value = false // 退出 合并模式

  combineChosing.value = [] // 重置 合并项
}

/**
 * 关闭 合并-排序-弹窗
 */
const closeCombineSortDialog = () => {
  combineDialogVisible.value = false

  if (combineSortable.value) {
    combineSortable.value.destroy() // 销毁
  }
}

/**
 * 优化：使用Map来快速查找选中的书本
 */
const combineChoosingMap = computed(() => {
  const map = new Map()
  combineChosing.value.forEach((book) => {
    map.set(book.id, true)
  })
  return map
})

/**
 * 检查书本是否被选中（优化性能）
 */
const isBookActive = (bookId: string | number) => {
  return combineChoosingMap.value.has(bookId)
}

/**
 * 点击 book-card
 */
const handleCombineBookClick = (book: Book) => {
  if (combineMode.value) {
    const existingIndex = combineChosing.value.findIndex((item) => item.id === book.id)
    if (existingIndex !== -1) {
      // 已存在，则移除
      combineChosing.value.splice(existingIndex, 1)
    } else {
      // 不存在，则添加
      combineChosing.value.push(book)
    }
  }
}

/**
 * 提交-合并
 */
const submitToCombine = async () => {
  const dataIds = Array.from(sortCombineRef.value?.children || []).map((el) =>
    el.getAttribute('data-id'),
  ) as []
  try {
    await combineBooksApi(dataIds)

    Toast.success('合并成功')
    closeCombineSortDialog() // 关闭 合并-排序 弹窗
    closeCombineMode() // 退出 合并模式

    loadData() // 刷新数据
  } catch (error) {}
}

/**
 * 计算书籍容器的上边距
 */
const bookContainerMarginTop = computed(() => {
  // 折叠状态：默认 80px
  // 展开状态：动态-操作栏高度 + 64px
  return isCollapse.value ? '80px' : `${oprBlockHeight.value + 64}px`
})

/**
 * 计算操作栏高度
 */
const calculateOprBlockHeight = () => {
  nextTick(() => {
    // 获取容器实际高度（确保DOM已经渲染完成）
    if (isCollapse.value) {
      // 折叠状态，则高度为 0
      oprBlockHeight.value = 0
      return
    }

    // 展开状态，则动态计算高度
    oprBlockHeight.value = oprBlockContainer.value?.offsetHeight || 0
  })
}

/**
 * 使用lodash的debounce函数更新form.key
 */
const updateFormKey = debounce((val: string) => {
  form.page = 1
  form.key = val
}, 600)

/**
 * 获取图书列表
 */
const loadData = async () => {
  try {
    const params = {
      ...form,
      tags: form.tags.join(';'),
    }
    const { list, count, is_end } = await getBookListApi(params)

    totalCount.value = count

    isEnd.value = is_end

    if (isLoadingMore.value) {
      // 下拉加载更多中...
      if (list && list.length > 0) {
        liBooks.value.push(...list) // 新增数据
      }
    } else {
      // 正常请求（搜索、重置、刷新等）
      liBooks.value = list

      // 确保 IntersectionObserver 正常工作
      nextTick(() => {
        initIntersectionObserver()
      })
    }

    isLoadingMore.value = false
  } catch (error) {
    console.error('获取图书列表失败:', error)
    isLoadingMore.value = false
  }
}

/**
 * 下拉加载更多数据
 */
const loadMoreData = async () => {
  if (isLoadingMore.value || isEnd.value) return

  isLoadingMore.value = true

  form.page += 1 // 页码+1，触发watch 请求数据
}

/**
 * 使用 Intersection Observer 优化滚动加载
 */
const loadMoreObserver = ref<IntersectionObserver | null>(null)
const loadMoreTrigger = ref<HTMLElement | null>(null)

/**
 * 初始化 Intersection Observer
 */
const initIntersectionObserver = () => {
  if (loadMoreObserver.value) {
    loadMoreObserver.value.disconnect()
  }

  loadMoreObserver.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && !isLoadingMore.value && !isEnd.value) {
          loadMoreData()
        }
      })
    },
    {
      rootMargin: '100px', // 提前100px触发加载
      threshold: 0.1,
    },
  )

  if (loadMoreTrigger.value) {
    loadMoreObserver.value.observe(loadMoreTrigger.value)
  }
}

/**
 * 重置
 */
const reset = () => {
  isReset.value = true // 打开重置模式

  hideImg.value = false

  key.value = ''

  form.key = ''
  form.page = 1
  form.size = PAGE_SIZE
  form.genre = ''
  form.tags = []
  form.no_subject = 0
  form.no_genre = 0
  form.no_tags = 0
  form.is_collect = 0
  form.is_random = 0

  isEnd.value = false // 重置为 false，允许重新加载
  isLoadingMore.value = false

  isReset.value = false // 关闭重置模式

  // 重新初始化 IntersectionObserver，确保滚动加载功能正常
  nextTick(() => {
    initIntersectionObserver()
  })
}

/**
 * 处理分类点击事件
 */
const handleGenreClick = (genre: string) => {
  form.genre = genre
}

/**
 * 处理标签点击事件
 */
const handleTagClick = (tag: string) => {
  form.tags = [tag]
}

/**
 * 切换 操作栏-折叠
 */
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value

  // 监听折叠状态变化，调整margin-top
  nextTick(() => {
    calculateOprBlockHeight()
  })
}

// 修改-类型 数据
const editGenreVisible = ref(false)
const editGenreMeta = ref<{ id: string | number; genre: string }>({ id: 0, genre: '' })
/**
 * 准备-修改-类型
 */
const readyToEditGenre = (bookMeta: Book) => {
  // 赋值
  editGenreMeta.value = {
    id: bookMeta.id,
    genre: bookMeta.genre as string,
  }
  // 打开 弹窗
  editGenreVisible.value = true
}

// 修改-标签 数据
const editTagsVisible = ref(false)
const editTagsMeta = ref<{ id: string | number; tags: Tag[] }>({ id: 0, tags: [] })
/**
 * 准备-修改-标签
 */
const readyToEditTags = (bookMeta: Book) => {
  // 赋值
  editTagsMeta.value = {
    id: bookMeta.id,
    tags: bookMeta.tags as [],
  }

  // 打开 弹窗
  editTagsVisible.value = true
}

// 修改-标签 数据
const editSubjectsVisible = ref(false)
const editSubjectsMeta = ref<{ id: string | number; li_subjects: Subject[] }>({
  id: 0,
  li_subjects: [],
})

/**
 * 准备-修改-专题
 */
const readyToEditSubjects = (bookMeta: Book) => {
  // 赋值
  editSubjectsMeta.value = {
    id: bookMeta.id,
    li_subjects: bookMeta.li_subjects,
  }

  // 打开 弹窗
  editSubjectsVisible.value = true
}

// 监听key变化
watch(key, (newVal) => {
  if (!isReset.value) {
    // 非重置模式，才赋值（防止重置请求2遍）
    updateFormKey(newVal)
  }
})

// 监听form变化，触发搜索
watch(
  form,
  () => {
    loadData()
  },
  { deep: true },
)

// on mounted
onMounted(async () => {
  // 检查URL中是否有key参数
  const queryKey = route.query.key
  if (queryKey && typeof queryKey === 'string') {
    key.value = queryKey
    form.key = queryKey
  }
  await Promise.all([bookMetaStore.fetchAllMeta(), loadData()]) // 加载 下拉 标签/类型、 加载图书

  // 初始化，计算动态高度
  calculateOprBlockHeight()

  // 添加resize事件监听
  window.addEventListener('resize', calculateOprBlockHeight)

  // 初始化 Intersection Observer（替代滚动事件监听）
  nextTick(() => {
    initIntersectionObserver()
  })
})

// 在组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', calculateOprBlockHeight)
  
  // 移除 Intersection Observer
  if (loadMoreObserver.value) {
    loadMoreObserver.value.disconnect()
  }
})
</script>

<style scoped lang="scss">
.home-container {
  width: 100%;
  margin: 0 auto;

  .collapse-btn {
    position: fixed;
    top: 64px;
    right: 4px;
    z-index: 9000;
    transition:
      top 0.2s ease,
      transform 0.2s ease;
  }

  .collapse-btn.collapsed {
    top: 68px; // 略微调整位置，使按钮在折叠时位置适当
    transform: rotate(180deg);
  }

  .opr-block-container {
    position: fixed;
    top: 60px; // 导航栏高度
    left: 0;
    right: 0;
    z-index: 9000;
    background-color: #f8f9fa;
    border-bottom: 1px solid #d4d4d4;
    padding: $gap;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e6e6e6;

    @media (max-width: $media-width) {
      padding: 0;
    }

    &.collapsed {
      opacity: 0;
      visibility: hidden;
      max-height: 0;
      padding-top: 0;
      padding-bottom: 0;
    }

    .opr-block {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
      gap: $gap;
      margin-right: 24px;

      .show-total-count {
        font-size: 12px;
        color: #666;
        border: 1px solid #c0c0c0;
        padding: 4px;
        border-radius: 4px;
      }

      .combine-block {
        margin-left: $gap;
      }

      @media (max-width: $media-width) {
        padding: 4px;
        margin-right: 20px;

        .opr-divider {
          display: none;
        }
      }

      .key-search {
        width: 180px;

        @media (max-width: $media-width) {
          width: 120px;
        }
      }

      .genre-select {
        width: 150px;

        @media (max-width: $media-width) {
          width: 120px;
        }
      }
    }
  }

  .book-container {
    max-width: 100%;
    background-color: white;
    padding: $gap * 2;
    // margin-top 现在通过 Vue 动态计算

    @media (max-width: $media-width) {
      padding: $gap;
    }

    .book-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
      gap: 32px;

      @media (max-width: $media-width) {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;
      }

      .book-item-in-home {
        &.combing {
          // border: 1px solid dotted;
          border: 2px dashed #c7c7c7;
        }
        &.active {
          $chose-color: #096b02;
          border: 2px solid $chose-color;
          box-shadow: 0 0 0 2px $chose-color;
        }
      }
    }
  }
}

.combine-block {
  .combine-block-item {
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border-radius: 6px;
    margin-bottom: 10px;
    padding: 6px 12px;
    cursor: grab;
    border: 1px solid #e0e0e0;
    transition:
      background 0.2s,
      box-shadow 0.2s;

    img {
      width: 40px;
      height: 40px;
      object-fit: cover;
      margin-right: 10px;
      vertical-align: middle;
    }
    span {
      font-size: 14px;
      color: #666;
    }
  }
}

// 加载更多触发器样式
.load-more-trigger {
  height: 20px;
  width: 100%;
}
</style>
