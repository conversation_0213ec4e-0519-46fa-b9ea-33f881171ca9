<template>
  <div class="subject-details-container">
    <!-- 基本信息 -->
    <div class="subject-info">
      <!-- 固定的操作栏 -->
      <div class="fix-opr-block">
        <!-- 排序、删除 按钮 -->
        <my-button
          type="danger"
          title="编辑模式"
          size="small"
          v-show="!editMode"
          @click="toggleEditMode"
        >
          <font-awesome-icon icon="fa-solid fa-gears" />
        </my-button>
        <div v-show="editMode" class="my_flex_start">
          <my-button title="退出" size="small" @click="toggleEditMode">
            <font-awesome-icon icon="fa-solid fa-xmark" />
          </my-button>
          <my-button type="success" title="提交排序" size="small" @click="submitToSort">
            <font-awesome-icon icon="fa-solid fa-check" />
          </my-button>
        </div>
      </div>

      <!-- 信息栏 -->
      <div class="head-box">
        <div class="title">{{ subject.title }}</div>

        <!-- 收集的书本数量 -->
        <div class="book-count">
          <font-awesome-icon icon="fa-solid fa-book"></font-awesome-icon>
          {{ subject.book_count }}
        </div>

        <!-- 时间 -->
        <div class="my_small_gray">
          {{ subject.created_at }}
        </div>
      </div>

      <!-- 图片显示 -->
      <div>
        <img :src="subject.thumb_url" class="subject-img" alt="" />
      </div>
    </div>

    <!-- 专题-书本-列表 -->
    <div class="show-book-box">
      <!-- 显示图片栏 -->
      <div class="book-list" ref="sortBoxRef" :key="listKey">
        <book-card
          v-for="book in subject.li_books"
          :key="book.id"
          :book="book"
          :data-id="book.id"
          :del-btn-visible="editMode"
          :is-simple="isMobile"
          :class="{ sorting: editMode }"
          @refresh-book="loadData(1)"
          @ready-to-edit-genre="readyToEditGenre"
          @ready-to-edit-tags="readyToEditTags"
          @handle-delete="handleDeleteBookFromSubject"
        />
      </div>
    </div>

    <!-- 弹窗 修改-类型 -->
    <edit-genre-dialog
      v-model:visible="editGenreVisible"
      :bookMeta="editGenreMeta"
      @on-close="editGenreVisible = false"
      @on-success="loadData(1)"
    />

    <!-- 弹窗 修改-标签 -->
    <edit-tags-dialog
      v-model:visible="editTagsVisible"
      :bookMeta="editTagsMeta"
      @on-close="editTagsVisible = false"
      @on-success="loadData(1)"
    />
  </div>
</template>

<script setup lang="ts">
import type { Book, Tag } from '@/types/book'
import Sortable from 'sortablejs'

const route = useRoute()
const subjectId = route.params.subject_id as string

const isMobile = ref(false) // 是否移动端

const updateIsMobile = () => {
  isMobile.value = window.innerWidth < 768
}

const subject = ref({
  id: 0,
  title: '',
  book_count: 0,
  thumb_url: '',
  asc_priority: 0,
  created_at: '',
  updated_at: '',
  li_books: [] as Book[],
})

// 排序-模式
const editMode = ref(false)
const sortBoxRef = ref<HTMLElement>()
let sortableInstance: Sortable | null = null
// 强制重新渲染的key
const listKey = ref(0)

// 修改-类型 数据
const editGenreVisible = ref(false)
const editGenreMeta = ref<{ id: string | number; genre: string }>({ id: 0, genre: '' })
/**
 * 准备-修改-类型
 */
const readyToEditGenre = (bookMeta: Book) => {
  // 赋值
  editGenreMeta.value = {
    id: bookMeta.id,
    genre: bookMeta.genre as string,
  }

  // 打开 弹窗
  editGenreVisible.value = true
}

// 修改-标签 数据
const editTagsVisible = ref(false)
const editTagsMeta = ref<{ id: string | number; tags: Tag[] }>({ id: 0, tags: [] })
/**
 * 准备-修改-标签
 */
const readyToEditTags = (bookMeta: Book) => {
  // 赋值
  editTagsMeta.value = {
    id: bookMeta.id,
    tags: bookMeta.tags as [],
  }

  // 打开 弹窗
  editTagsVisible.value = true
}

/**
 * 从专题移除书本（优化版本）
 */
const handleDeleteBookFromSubject = async (bookMeta: Book) => {
  try {
    await delSubjectBookApi(subjectId, bookMeta.id)

    Toast.success('移除成功')

    // 本地更新数据，避免重新请求
    subject.value.li_books = subject.value.li_books.filter((book) => book.id !== bookMeta.id)
    subject.value.book_count = subject.value.li_books.length
  } catch (error) {
    console.error('移除书本失败:', error)
    Toast.error('移除失败，请重试')
  }
}

/**
 * 切换排序模式（修复取消恢复bug）
 */
const toggleEditMode = async () => {
  if (!editMode.value) {
    // 进入排序模式
    editMode.value = true

    // 初始化拖动
    if (sortBoxRef.value) {
      sortableInstance = Sortable.create(sortBoxRef.value, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
      })
    }
  } else {
    // 先销毁sortable实例，停止监听DOM变化
    if (sortableInstance) {
      sortableInstance.destroy()
      sortableInstance = null
    }

    // 退出编辑模式
    editMode.value = false

    // 强制重新渲染DOM
    nextTick(() => {
      listKey.value += 1
    })
  }
}

/**
 * 提交排序
 */
const submitToSort = async () => {
  try {
    const dataIds = Array.from(sortBoxRef.value?.children || []).map((el) =>
      el.getAttribute('data-id'),
    ) as []

    await sortSubjectsBooksApi(subjectId, dataIds)

    Toast.success('排序成功')

    // 退出编辑模式
    editMode.value = false

    // 销毁sortable实例
    if (sortableInstance) {
      sortableInstance.destroy()
      sortableInstance = null
    }

    // 重新加载数据以获取最新的排序结果
    await loadData()

    // 更新渲染key
    listKey.value += 1
  } catch (err) {
    console.error('排序失败:', err)
    Toast.error('排序失败，请重试')
  }
}

/**
 * 加载专题数据
 */
const loadData = async (noCache: number = 0) => {
  try {
    const res = await getSubjectDetailsApi(subjectId, noCache)
    subject.value = res.subject
  } catch (err) {
    console.error('加载专题数据失败:', err)
    Toast.error('加载数据失败，请重试')
  }
}

onMounted(() => {
  updateIsMobile() // 刷新 isMobile

  loadData()

  // 监听 宽度
  window.addEventListener('resize', updateIsMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateIsMobile)

  // 清理sortable实例
  if (sortableInstance) {
    sortableInstance.destroy()
    sortableInstance = null
  }
})
</script>

<style lang="scss" scoped>
.subject-details-container {
  padding: $gap;
  min-height: 400px;

  @media (max-width: $media-width) {
    padding: 0;
  }

  .subject-info {
    padding: $gap;
    position: relative;

    .fix-opr-block {
      position: absolute;
      top: $gap;
      right: $gap * 2;
      text-align: center;
      font-size: 18px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: $gap * 4;
      width: 40px;

      @media (max-width: $media-width) {
        gap: $gap * 2;
      }
    }

    @media (max-width: $media-width) {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }

    .head-box {
      @include flex-start;
      gap: $gap * 2;
      margin-bottom: $gap;

      @media (max-width: $media-width) {
        margin-bottom: unset;
      }

      .title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .book-count {
        font-size: 12px;
        color: #999;
        margin-left: 4px;
      }
    }

    .subject-img {
      object-fit: contain;
      max-width: 200px;
      max-height: 200px;
      border-radius: 4px;
      box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px;
    }
  }

  .show-book-box {
    // margin-top: $gap;
    border-top: 1px solid #ebebeb;
    padding: $gap;

    .book-list {
      display: flex;
      flex-wrap: wrap;
      gap: $gap * 2;

      @media (max-width: $media-width) {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;
      }

      .sorting {
        cursor: move;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.02);
        }
      }
    }
  }
}

// Sortable拖拽样式
.sortable-ghost {
  opacity: 0.5;
  background: #f0f0f0;
}

.sortable-chosen {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.sortable-drag {
  transform: rotate(1deg);
}
</style>
