<template>
  <div class="upload-container">
    <div class="upload-body">
      <!-- 多文件上传 -->
      <div class="upload-section">
        <div class="upload-box">
          <my-upload
            ref="uploadRef"
            :multiple="true"
            :accept="['zip', 'rar', '7z']"
            :max-size="50 * 1024 * 1024"
            :max-files="5"
            @files-selected="handleMultiFilesSelected"
            @file-cleared="handleMultiFileCleared"
            @error="handleError"
          />
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <my-button type="danger" @click="reset">清空</my-button>
      <my-button type="primary" @click="submit">上传</my-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// MyUpload 组件的类型定义
interface MyUploadInstance {
  clearFile: () => void
}

// 响应式数据
const uploadRef = ref<MyUploadInstance | null>(null)
const selectedFiles = ref<File[]>([])

// 多文件选择处理函数
const handleMultiFilesSelected = (files: File[]) => {
  selectedFiles.value = files
  console.log('多文件选择:', files)
}

// 多文件清空处理函数
const handleMultiFileCleared = () => {
  selectedFiles.value = []
  console.log('多文件已清空')
}

// 错误处理
const handleError = (error: { message: string }) => {
  Toast.error(error.message)
}

// 上传文件函数
const submit = async () => {
  if (selectedFiles.value.length === 0) {
    Toast.error('请先选择文件')
    return
  }

  try {
    // 多文件上传
    const formData = new FormData()
    selectedFiles.value.forEach((file) => {
      formData.append('file', file)
    })
    const { repeat } = await uploadBooksApi(formData)
    if (repeat.length > 0) {
      Toast.success('文件上传成功，重复文件: ' + repeat.join(','))
      console.warn('重复文件: ' + repeat.join(','))
    } else {
      Toast.success('文件上传成功')
    }

    reset() // 上传成功后清空文件
  } catch (error: any) {
    console.error('上传失败:', error)
    Toast.error('上传失败：' + (error?.message || '未知错误'))
  }
}

// 清空所有文件
const reset = () => {
  uploadRef.value?.clearFile()
}
</script>

<style scoped lang="scss">
.upload-container {
  padding: $gap * 4;
  margin: 0 auto;
  max-width: 800px;
}

.upload-body {
  display: flex;
  gap: $gap * 4;
  justify-content: center;
}

.upload-section {
  margin-bottom: 40px;

  h3 {
    margin-bottom: 16px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
  }

  .upload-box {
    @include flex-center;
  }
}

.file-result {
  margin-top: 16px;
  padding: 12px;
  background-color: #e8f5e8;
  border-radius: 4px;
  border-left: 4px solid #67c23a;
}

.file-result p {
  margin: 0 0 8px 0;
  color: #333;
}

.file-result button {
  background-color: #409eff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.file-result button:hover {
  background-color: #337ecc;
}

.actions {
  text-align: center;
  margin-top: 30px;
}

.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #f56c6c;
  color: white;
  padding: 12px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}
</style>
