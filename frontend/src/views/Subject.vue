<template>
  <div class="subjects-container">
    <!-- 操作栏 -->
    <div class="opr-block">
      <my-input v-model="key" class="opr-search" placeholder="搜索专题" clear />

      <!-- 添加-专题-按钮 -->
      <my-button
        type="primary"
        title="添加"
        size="small"
        @click="() => (addSubjectDialog.visible = true)"
      >
        <font-awesome-icon icon="fa-solid fa-plus" />
      </my-button>

      <!-- 排序 -->
      <div>
        <my-button
          type="primary"
          title="排序"
          size="small"
          v-show="!sortMode"
          @click="toggleSortMode"
        >
          <font-awesome-icon icon="fa-solid fa-arrow-up-wide-short" />
        </my-button>
        <div v-show="sortMode" class="my_flex_start">
          <my-button title="取消排序" size="small" @click="toggleSortMode">
            <font-awesome-icon icon="fa-solid fa-xmark" />
          </my-button>
          <my-button type="success" title="提交排序" size="small" @click="submitToSort">
            <font-awesome-icon icon="fa-solid fa-check" />
          </my-button>
        </div>
      </div>

      <!-- 重置 -->
      <my-button type="danger" @click="reset" class="reset-btn" size="small" title="重置">
        <font-awesome-icon icon="fa-solid fa-undo" />
      </my-button>
    </div>

    <!-- 显示专题 -->
    <div class="subject-show-box" ref="sortBoxRef">
      <div class="single-subject" v-for="item in liSubjects" :key="item.id" :data-id="item.id">
        <!-- 图片 -->
        <div class="single-subject--img">
          <router-link :to="`/subject/${item.id}`">
            <img src="@/assets/img/blank.png" :data-src="item.thumb_url" class="lozad" />
          </router-link>
        </div>
        <div class="single-subject--info">
          <div class="title">
            <font-awesome-icon icon="fa-solid fa-layer-group" />
            {{ item.title }}
          </div>
          <div class="book-count">
            <font-awesome-icon icon="fa-solid fa-book"></font-awesome-icon>
            {{ item.book_count }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加-专题 -->
  <my-dialog
    v-model="addSubjectDialog.visible"
    title="添加-专题"
    @close="() => (addSubjectDialog.visible = false)"
    width="400px"
  >
    <div>
      <my-input v-model="addSubjectDialog.value" clear></my-input>
    </div>

    <template #footer>
      <div class="my_flex_end" style="gap: 8px">
        <my-button @click="() => (addSubjectDialog.visible = false)">取 消</my-button>
        <my-button type="primary" @click="submitToAddSubject">确 定</my-button>
      </div>
    </template>
  </my-dialog>
</template>

<script setup lang="ts">
import debounce from 'lodash/debounce'
import lozad from 'lozad'
import Sortable from 'sortablejs'

const isReset = ref<boolean>(false)

const key = ref<string>('')

const liSubjects = ref<any[]>([])
const totalCount = ref<number>(0)

// 添加-专题-弹窗
const addSubjectDialog = ref({
  visible: false,
  value: '',
})

const form = reactive({
  key: '',
  page: 1,
  size: 50,
})

// 排序-模式
const sortMode = ref(false)

const sortBoxRef = ref<HTMLElement>()

// 切换 排序-模式
const toggleSortMode = () => {
  sortMode.value = !sortMode.value

  if (sortMode.value && sortBoxRef.value) {
    // 进入排序模式，初始化拖动
    Sortable.create(sortBoxRef.value, {
      animation: 150,
    })
  } else {
    // 退出排序模式
    location.reload()
  }
}

/**
 * 提交-排序
 */
const submitToSort = async () => {
  try {
    const dataIds = Array.from(sortBoxRef.value?.children || []).map((el) =>
      el.getAttribute('data-id'),
    ) as []

    await sortSubjectsApi(dataIds)

    // 刷新
    location.reload()
  } catch (err) {}
}

/**
 * 添加-专题
 */
const submitToAddSubject = async () => {
  try {
    await addSubjectApi(addSubjectDialog.value.value)

    addSubjectDialog.value.visible = false // 关闭
    addSubjectDialog.value.value = '' // 重置

    loadData() // 刷新
  } catch (error) {}
}

/**
 * 使用lodash的debounce函数更新form.key
 */
const updateFormKey = debounce((val: string) => {
  form.page = 1
  form.key = val
}, 600)

// 监听key变化
watch(key, (newVal) => {
  updateFormKey(newVal)
})

// 监听form变化，触发搜索
watch(
  form,
  () => {
    loadData()
  },
  { deep: true },
)

/**
 * 重置
 */
const reset = () => {
  isReset.value = true // 打开重置模式

  key.value = ''
  form.key = ''
  form.page = 1
  form.size = 50
}

const loadData = async () => {
  try {
    const { list, count } = await getSubjectsListApi({ ...form })

    liSubjects.value = list
    totalCount.value = count

    // 后续 滚动翻页，需要 initLozad()
  } catch (error) {}
}

// lozad 懒加载观察器
let lozadObserver: any = null

/**
 * 初始化或重新初始化 lozad 懒加载
 */
const initLozad = () => {
  nextTick(() => {
    // 如果已有观察器，先断开连接
    if (lozadObserver) {
      lozadObserver.observer.disconnect()
    }

    // 创建新的观察器并观察所有 .lozad 元素
    lozadObserver = lozad('.lozad')
    lozadObserver.observe()
  })
}

onMounted(async () => {
  await loadData()

  // 初始化 lozad 懒加载
  initLozad()
})

onUnmounted(() => {
  // 清理 lozad 观察器
  if (lozadObserver) {
    lozadObserver.observer.disconnect()
  }
})
</script>

<style scoped lang="scss">
.subjects-container {
  padding: 8px 16px;
  min-height: 400px;

  @media (max-width: $media-width) {
    padding: 4px;
  }

  .opr-block {
    @include flex-start;
    gap: $gap;

    .opr-search {
      width: 200px;

      @media (max-width: $media-width) {
        width: 150px;
      }
    }
  }

  .subject-show-box {
    display: flex;
    flex-wrap: wrap;
    gap: $gap * 2;
    margin-top: $gap;

    @media (max-width: $media-width) {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
      gap: $gap;
      padding: 4px;
    }

    .single-subject {
      background: #fff;
      border-radius: 6px;
      box-shadow:
        rgba(0, 0, 0, 0.1) 0px 1px 3px 0px,
        rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2px solid rgba(150, 150, 150, 0.05);

      &:hover {
        transform: translateY(-2px);
        box-shadow:
          rgba(0, 0, 0, 0.1) 0px 10px 15px -3px,
          rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
        border: 2px solid #6800b8;
      }

      &--img {
        width: 100%;
        height: 180px;
        min-width: 150px;
        max-width: 250px;
        overflow: hidden;
        border-radius: 4px;
        cursor: pointer;

        @media (max-width: $media-width) {
          height: 120px;
          min-width: unset;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      &--info {
        @include flex-between;

        .title {
          font-size: 14px;
          font-weight: bold;
          color: #333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          @media (max-width: $media-width) {
            font-size: 11px;
          }
        }
        .book-count {
          @include small-gray;

          @media (max-width: $media-width) {
            font-size: 11px;
          }
        }
      }
    }
  }
}
</style>
