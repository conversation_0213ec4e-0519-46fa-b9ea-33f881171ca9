<template>
  <div class="book-details-container">
    <!-- 顶部基础 信息栏 -->
    <div class="book-info-block">
      <div class="fix-opr-block">
        <font-awesome-icon
          icon="fa-solid fa-star"
          class="opr-btn-item btn-collect"
          :class="{ active: book.is_collect }"
          @click="toggleCollect"
        />
        <font-awesome-icon
          class="opr-btn-item btn-download"
          icon="fa-solid fa-download"
          @click="downloadBook"
        />
        <div>
          <font-awesome-icon
            v-show="!manageMode"
            class="opr-btn-item btn-manage"
            icon="fa-solid fa-gears"
            @click="toggleManageMode"
          />
          <div v-show="manageMode" class="btn-manage-block">
            <font-awesome-icon
              class="opr-btn-item btn-manage--check"
              icon="fa-solid fa-check"
              @click="sortBookPage"
            />
            <font-awesome-icon
              class="opr-btn-item btn-manage--cancle"
              icon="fa-solid fa-xmark"
              @click="toggleManageMode"
            />
          </div>
        </div>
        <font-awesome-icon
          class="opr-btn-item btn-del"
          icon="fa-solid fa-trash-can"
          @click="() => (delBookVisible = true)"
        />
      </div>

      <div class="head-block">
        <div class="title-block">
          <div class="title">{{ book.title }}</div>
          <font-awesome-icon
            class="btn-edit-title"
            icon="fa-solid fa-edit"
            @click="readyToEditTitle"
          />
        </div>
        <!-- 图片量 -->
        <div class="pages">
          <font-awesome-icon icon="fa-solid fa-image"></font-awesome-icon>
          <span>{{ book.page_count }}</span>
        </div>
        <!-- 浏览量icon -->
        <div class="views">
          <font-awesome-icon icon="fa-solid fa-eye"></font-awesome-icon>
          <span>{{ book.views }}</span>
        </div>
      </div>

      <!-- 类型 -->
      <div class="show-block">
        <div>类型:</div>
        <div class="genre-block">
          <div class="genre">{{ book.genre }}</div>
          <!-- 编辑-类型-btn -->
          <font-awesome-icon
            class="edit-btn"
            icon="fa-solid fa-edit"
            @click="() => (editGenreVisible = true)"
          ></font-awesome-icon>
        </div>
      </div>

      <!-- 标签 -->
      <div class="show-block">
        <div>标签:</div>
        <div class="tag-block">
          <div class="show-tags">
            <div v-for="tag in book.tags" :key="tag.id || tag.value" class="single-tag">
              {{ tag.value }}
            </div>
          </div>
          <!-- 编辑-标签-btn -->
          <font-awesome-icon
            class="edit-btn"
            @click="() => (editTagsVisible = true)"
            icon="fa-solid fa-edit"
          ></font-awesome-icon>
        </div>
      </div>

      <!-- 专题 -->
      <div class="show-block">
        <div>专题:</div>
        <div class="subject-block">
          <div class="show-subject">
            <router-link
              class="subject-link"
              v-for="subject in book.li_subjects"
              :key="subject.id"
              :to="`/subject/${subject.id}`"
            >
              {{ subject.title }}
            </router-link>
          </div>
          <!-- 编辑-类型-btn -->
          <font-awesome-icon
            class="edit-btn"
            icon="fa-solid fa-edit"
            @click="() => (editSubjectVisible = true)"
          />
        </div>
      </div>
      <!-- 备注 -->
      <div class="show-block" v-show="book.remarks">
        <div>备注:</div>
        <div>{{ book.remarks }}</div>
      </div>
      <!-- 时间 -->
      <div class="show-block">
        <div>创建:</div>
        <div class="my_small_gray">{{ book.created_at }}</div>
      </div>
      <div class="show-block">
        <div>修改:</div>
        <div class="my_small_gray">{{ book.updated_at }}</div>
      </div>
      <!-- 方向 -->
      <div class="show-block">
        <div>方向:</div>
        <div>
          <div class="btn-groups" @click="toggleDirection">
            <!-- 按钮组，单选，由 横屏、竖屏 组成 -->
            <div :class="{ active: direction === 0 }">横屏</div>
            <div :class="{ active: direction === 1 }">竖屏</div>
          </div>
        </div>
      </div>
      <!-- 尺寸 -->
      <div class="show-block">
        <div>尺寸:</div>
        <div>
          <!-- 尺寸调节滑块 -->
          <div class="size-slider-container">
            <button class="size-slider-btn" @click="() => (sizeSlider -= 1)">
              <font-awesome-icon icon="fa-solid fa-minus"></font-awesome-icon>
            </button>
            <input
              v-model="sizeSlider"
              type="range"
              class="size-slider"
              min="0"
              max="100"
              value="20"
            />
            <button class="size-slider-btn" @click="() => (sizeSlider += 1)">
              <font-awesome-icon icon="fa-solid fa-plus"></font-awesome-icon>
            </button>
            <span class="size-slider-value">{{ sizeSlider }}</span>
          </div>
        </div>
      </div>
    </div>

    <hr />

    <!-- book内容 -->
    <div class="book-content-images">
      <div class="images-block" :class="{ 'images-block--vertical': direction }" ref="sortRef">
        <div
          v-for="page_item in liPages"
          :key="page_item.id"
          class="img-box"
          :class="{ 'img-box--vertical': direction }"
          :data-id="page_item.id"
        >
          <img
            src="@/assets/img/blank.png"
            :data-src="page_item.img_path"
            class="single-book-img lozad"
            :style="bookImgStyle"
            :alt="`page:${page_item.page}`"
            @click="openPreview(page_item.img_path)"
          />

          <!-- 删除单页 -->
          <div class="btn-del-page" v-show="manageMode">
            <font-awesome-icon icon="fa-solid fa-trash-can" @click="delBookPage(page_item.id)" />
          </div>
        </div>
      </div>

      <!-- 加载更多触发器 -->
      <div ref="loadMoreTrigger" class="load-more-trigger" v-if="!isEnd"></div>
    </div>

    <!-- 专题-显示 -->
    <div v-for="subject_item in book.li_subject_book" :key="subject_item.id" class="single-subject">
      <my-divider></my-divider>
      <div>
        <div class="subject-head-block">
          <font-awesome-icon icon="fa-solid fa-layer-group"></font-awesome-icon>
          <router-link class="subject-head-block--title" :to="`/subject/${subject_item.id}`">{{
            subject_item.title
          }}</router-link>
        </div>
        <!-- 专题-书本 -->
        <div class="subject-book-block">
          <a
            :href="`/book/${s_book.id}`"
            v-for="s_book in subject_item.li_books"
            :key="s_book.id"
            class="single-subject-book"
            :class="{ active: s_book.id === book.id }"
            :title="s_book.title"
          >
            <img src="@/assets/img/blank.png" :data-src="s_book.first_img" class="lozad" />
            <span class="my_just_2_line">{{ s_book.asc_priority }} {{ s_book.title }}</span>
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- 弹窗 修改-类型 -->
  <edit-genre-dialog
    v-model:visible="editGenreVisible"
    :bookMeta="{ id: book.id, genre: book.genre }"
    @on-close="() => (editGenreVisible = false)"
    @on-success="loadData"
  />

  <!-- 弹窗 修改-标签 -->
  <edit-tags-dialog
    v-model:visible="editTagsVisible"
    :bookMeta="{ id: book.id, tags: book.tags }"
    @on-close="() => (editTagsVisible = false)"
    @on-success="loadData"
  />

  <!-- 弹窗 修改-专题 -->
  <edit-subjects-dialog
    v-model:visible="editSubjectVisible"
    :bookMeta="{ id: book.id, li_subjects: book.li_subjects }"
    @on-close="() => (editSubjectVisible = false)"
    @on-success="loadData"
  />

  <!-- 修改-标题 -->
  <my-dialog v-model="editTitleVisible" title="修改标题" @close="() => (editTitleVisible = false)">
    <div>
      <my-input v-model="editTitleValue" clear></my-input>
    </div>

    <template #footer>
      <div class="my_flex_end">
        <my-button @click="() => (editTitleVisible = false)">取 消</my-button>
        <my-button type="primary" @click="submitToEditTitle">确 定</my-button>
      </div>
    </template>
  </my-dialog>

  <!-- 删除-确认 -->
  <my-dialog title="警告" v-model="delBookVisible" @close="closeDelDialog" :width="300">
    <div>即将删除整本书，是否继续？</div>

    <template #footer>
      <div class="my_flex_end">
        <my-button @click="closeDelDialog">取 消</my-button>
        <my-button type="danger" @click="delBook">确 定</my-button>
      </div>
    </template>
  </my-dialog>

  <!-- 预览 -->
  <my-preview v-model:visible="isPreviewVisible" :preview-image="previewImage" />
</template>

<script setup lang="ts">
import type { Book, BookPage, Subject, Tag } from '@/types/book'
import lozad from 'lozad'
import Sortable from 'sortablejs'

const route = useRoute()

const bookId = route.params.book_id as string

const book = ref({
  id: '',
  title: '',
  first_img: '',
  genre: '',
  tags: [] as Tag[],
  is_collect: false,
  page_count: 0,
  views: 0,
  li_subjects: [] as Subject[],
  remarks: '',
  created_at: '',
  updated_at: '',
  li_subject_book: [{ id: 0, title: '', li_books: [] as Book[] }],
})

const direction = ref(0) // 方向：0-横屏 1-竖屏

const isMobile = ref(false) // 是否移动端

/**
 * 更新 是否移动端
 */
const updateIsMobile = () => {
  isMobile.value = window.innerWidth < 768
}

const sizeSlider = ref(0) // 滑块值

// 修改-标题 弹窗
const editTitleVisible = ref(false)
const editTitleValue = ref('')

// 修改-类型 弹窗
const editGenreVisible = ref(false)

// 修改-标签 弹窗
const editTagsVisible = ref(false)

// 修改-专题 弹窗
const editSubjectVisible = ref(false)

// 删除-书本 确认
const delBookVisible = ref(false)

// 操作模式
const manageMode = ref(false)

// 图片预览
const isPreviewVisible = ref(false)
const previewImage = ref<string>('')

/**
 * 打开-预览
 */
const openPreview = (imgPath: string) => {
  previewImage.value = imgPath // 赋值
  isPreviewVisible.value = true // 打开
}

/**
 * 准备 修改-标题
 */
const readyToEditTitle = () => {
  editTitleVisible.value = true
  editTitleValue.value = book.value.title
}

/**
 * 提交 修改-标题
 */
const submitToEditTitle = async () => {
  try {
    await editBookTitleApi(bookId, editTitleValue.value)

    Toast.success('修改成功')

    editTitleVisible.value = false // 关闭

    loadData() // 刷新
  } catch (error) {
    console.error(error)
  }
}

/**
 * 单个图片渲染，动态样式（优化版本）
 */
const bookImgStyle = computed(() => {
  const isHorizontal = direction.value === 0
  const sliderValue = sizeSlider.value

  if (isMobile.value) {
    // 移动端
    return isHorizontal
      ? { height: `${sliderValue * 4}px`, maxWidth: '100%' }
      : { width: `${sliderValue}%` }
  } else {
    // PC端
    return isHorizontal
      ? { height: `${sliderValue * 10}px`, maxWidth: '100%' }
      : { width: `${sliderValue * 10}px`, maxWidth: '100%' }
  }
})

/**
 * 请求 book-详情
 */
const loadData = async () => {
  try {
    const res = await getBookDetailsApi(bookId)
    book.value = res.book
  } catch (error) {
    console.error(error)
  }
}

// 滚动分页参数
const page = ref<number>(1)
const size = ref<number>(20)
const liPages = ref<BookPage[]>([])
const totalCount = ref<number>(0)
const isLoadingMore = ref(false) // 是否正在加载更多
const isEnd = ref(false) // 是否 已经无数据了，达到末页

// lozad 懒加载观察器
let lozadObserver: any = null

/**
 * 初始化或重新初始化 lozad 懒加载（优化版本）
 */
const initLozad = () => {
  nextTick(() => {
    if (!lozadObserver) {
      // 首次创建观察器
      lozadObserver = lozad('.lozad', {
        loaded: (el) => {
          // 图片加载完成后的回调，可以添加淡入效果
          el.classList.add('loaded')
        }
      })
    }

    // 重新观察所有 .lozad 元素（包括新添加的）
    lozadObserver.observe()
  })
}

/**
 * 请求 book 页面-列表
 */
const loadPagesList = async () => {
  try {
    const { list, count, is_end } = await getBookPagesListApi(bookId, {
      page: page.value,
      size: size.value,
    })

    totalCount.value = count
    isEnd.value = is_end

    if (isLoadingMore.value) {
      if (list && list.length > 0) {
        liPages.value?.push(...list) // 下拉，新增
      }
    } else {
      liPages.value = list // 正常请求，赋值
    }
    isLoadingMore.value = false

    // 重新初始化 lozad 以观察新添加的图片元素
    initLozad()
  } catch (error) {
    isLoadingMore.value = false
  }
}

/**
 * 下载-book
 */
const downloadBook = async () => {
  try {
    const response = await downloadBookApi(bookId)
    const blob = response.data as Blob

    let filename = `${book.value.title}.zip`

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    // 错误提示在拦截器里做了，这里兜底
    console.error(error)
  }
}

const sortRef = ref<HTMLElement>()

/**
 * 控制模式
 */
const toggleManageMode = () => {
  manageMode.value = !manageMode.value

  if (manageMode.value && sortRef.value) {
    // 进入管理模式，初始化拖动
    Sortable.create(sortRef.value, {
      animation: 150,
    })
  } else {
    location.reload()
  }
}

/**
 * 书本-页面-排序
 */
const sortBookPage = async () => {
  // 新的 page_id 数组
  const liPageId = Array.from(sortRef.value?.children || []).map((item) =>
    item.getAttribute('data-id'),
  ) as []

  try {
    await bookPagesSortApi(bookId, liPageId)

    Toast.success('排序成功')

    location.reload() // 刷新
  } catch (error) {
    console.error('排序失败:', error)
    Toast.error('排序失败，请重试')
  }
}

/**
 * 关闭 删除 弹窗
 */
const closeDelDialog = () => {
  delBookVisible.value = false
}

/**
 * 删除 书本
 */
const delBook = async () => {
  try {
    await delBookApi(bookId)

    delBookVisible.value = false

    Toast.success('删除成功，3s内关闭页面')
    setTimeout(() => {
      window.close()
    }, 3000)
  } catch (error) {}
}

/**
 * 删除 book-单页
 */
const delBookPage = async (page_id: number) => {
  try {
    await delBookPageApi(bookId, page_id)

    // 刷新
    loadPagesList()
  } catch (error) {}
}

/**
 * 切换 方向
 */
const toggleDirection = () => {
  if (direction.value) {
    // 本是竖屏，则切换为横屏
    direction.value = 0

    // 设置滑块默认值
    sizeSlider.value = 24
  } else {
    // 本是横屏，则切换为竖屏
    direction.value = 1

    if (isMobile.value) {
      sizeSlider.value = 100 // 竖屏+移动端 则直接为100
    } else {
      // 设置滑块默认值
      sizeSlider.value = 60
    }
  }

  // todo: setImgSize()
}

/**
 * 切换收藏
 */
const toggleCollect = async () => {
  try {
    const res = await toggleCollectApi(bookId)

    Toast.success(`${res === 1 ? '收藏' : '取消'}成功`)

    // 刷新
    loadData()
  } catch (error) {
    Toast.error(error as string)
  }
}

/**
 * 下拉加载更多数据
 */
const loadMoreData = async () => {
  if (isLoadingMore.value || isEnd.value) return

  isLoadingMore.value = true

  page.value += 1 // 页码+1

  loadPagesList()
}

/**
 * 使用 Intersection Observer 优化滚动加载
 */
const loadMoreObserver = ref<IntersectionObserver | null>(null)
const loadMoreTrigger = ref<HTMLElement | null>(null)

/**
 * 初始化 Intersection Observer
 */
const initIntersectionObserver = () => {
  if (loadMoreObserver.value) {
    loadMoreObserver.value.disconnect()
  }

  loadMoreObserver.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && !isLoadingMore.value && !isEnd.value) {
          loadMoreData()
        }
      })
    },
    {
      rootMargin: '200px', // 提前200px触发加载
      threshold: 0.1,
    },
  )

  if (loadMoreTrigger.value) {
    loadMoreObserver.value.observe(loadMoreTrigger.value)
  }
}

const init = () => {
  updateIsMobile() // 刷新是否移动端

  // 根据设备类型设置默认值
  direction.value = isMobile.value ? 1 : 0 // 移动端竖屏，PC端横屏
  sizeSlider.value = isMobile.value ? 100 : 24 // 移动端100，PC端24
}

onMounted(async () => {
  init() // 初始化 移动端，图片尺寸

  // 加载数据
  await Promise.all([loadData(), loadPagesList()])

  // 初始化 lozad 懒加载
  initLozad()

  // 监听 宽度
  window.addEventListener('resize', updateIsMobile)

  // 初始化 Intersection Observer（替代滚动事件监听）
  nextTick(() => {
    initIntersectionObserver()
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', updateIsMobile)

  // 清理 lozad 观察器
  if (lozadObserver) {
    lozadObserver.observer.disconnect()
  }

  // 移除 滚动翻页 Observer
  if (loadMoreObserver.value) {
    loadMoreObserver.value.disconnect()
  }
})
</script>

<style lang="scss" scoped>
.book-details-container {
  padding: 8px 16px 40px;
  min-height: 400px;

  @media (max-width: $media-width) {
    padding: 4px 4px 16px;
  }
}
.book-info-block {
  position: relative;

  .fix-opr-block {
    position: absolute;
    top: $gap;
    right: 0;
    text-align: center;
    font-size: 18px;
    display: flex;
    flex-direction: column;
    gap: $gap * 4;
    width: 40px;

    @media (max-width: $media-width) {
      gap: $gap * 2;
    }

    .opr-btn-item {
      color: #908f8f;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
        opacity: 0.9;
      }
    }

    .btn-collect.active {
      color: $red;
    }

    .btn-download {
      color: $green;
    }
    .btn-manage {
      color: $blue-light;
    }

    .btn-manage-block {
      display: flex;
      justify-content: space-between;
      gap: 4px;

      .btn-manage--check {
        color: orange;
      }
      .btn-manage--cancle {
        color: gray;
      }
    }
    .btn-del {
      color: $red;
    }
  }

  .head-block {
    @include flex-start;
    margin-bottom: 4px;
    margin-right: 40px;

    .title-block {
      display: flex;
      align-items: center;
      // margin-right: $gap * 4;
      gap: $gap * 4;

      @media (max-width: $media-width) {
        gap: 4px;
      }

      .title {
        font-size: 20px;
        font-weight: 600;

        @media (max-width: $media-width) {
          font-size: 16px;
        }
      }

      .btn-edit-title {
        visibility: hidden;
        color: $blue-light;
        cursor: pointer;
      }

      &:hover .btn-edit-title {
        visibility: visible;
      }
    }

    .pages,
    .views {
      @include flex-start;
      @include small-gray;
      margin-left: $gap;
      span {
        margin-left: 4px;
      }
    }
  }

  .show-block {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 13px;

    & > div:first-child {
      width: 40px;
      margin-right: 4px;
      font-size: 13px;
      color: gray;
    }
  }

  .tag-block,
  .genre-block,
  .subject-block {
    @include flex-start;
    flex: 1;
    font-size: 14px;

    .edit-btn {
      margin-left: $gap * 4;
      visibility: hidden;
      color: $blue-light;
      cursor: pointer;
    }

    &:hover .edit-btn {
      visibility: visible;
    }

    .genre {
      color: #df660e;
      font-weight: 600;
    }

    .show-tags {
      @include flex-start;
      gap: $gap;

      .single-tag {
        padding: 0px 4px;
        background-color: #28a745;
        color: white;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.3s ease;
        white-space: nowrap;
        flex-shrink: 0; /* 防止标签被压缩 */
      }
    }
    .show-subject {
      @include flex-start;
      gap: $gap;

      .subject-link {
        text-decoration: unset;
        font-weight: 600;
        font-size: 13px;
        color: #3e023e;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

.btn-groups {
  display: flex;
  border: 1px solid #dbdbdb;
  border-radius: 2px;

  div {
    padding: 4px 8px;

    transition: all 0.2s ease;
    font-weight: 500;
    cursor: pointer;

    background-color: #ffffff;
    color: #434343;

    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #fafafa;
      // border-color: #999999;
      color: #333333;
    }
    &.active {
      background-color: #3b96f3;
      color: white;
      transform: scale(0.98);
    }
  }
}

.book-content-images {
  .images-block {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    &--vertical {
      display: flex;
      flex-direction: column;
      align-items: center;
      // gap: unset !important;
    }

    @media (max-width: $media-width) {
      gap: 0;
    }

    .img-box {
      position: relative;
      border: 1px solid #c4c4c4;
      display: flex;
      justify-content: center;

      &--vertical {
        @media (max-width: $media-width) {
          width: 100%;
          border: unset;
        }
      }

      &:hover {
        border: 1px solid #808080;
      }

      .btn-del-page {
        @include flex-center;

        position: absolute;
        right: 4px;
        top: 4px;
        color: white;
        cursor: pointer;
        width: 20px;
        height: 20px;
        font-size: 12px;
        background-color: $red;
        border-radius: 50%;
      }
    }
  }
}

// 滑块
/* 尺寸滑块容器 */
.size-slider-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 尺寸调节按钮 */
.size-slider-btn {
  width: 16px;
  height: 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.size-slider-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
  color: #4a90e2;
}

.size-slider-btn:active {
  background: #e8e8e8;
  transform: scale(0.95);
}

/* 滑块样式 */
.size-slider {
  -webkit-appearance: none;
  width: 120px;
  height: 4px;
  border-radius: 2px;
  background: #ddd;
  outline: none;
}

.size-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  transition: all 0.2s ease;
}

.size-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  background: #357abd;
}

.size-slider-value {
  min-width: 24px;
  font-size: 13px;
  color: #666;
}

hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.single-subject {
  .subject-head-block {
    @include flex-start;
    gap: $gap;
    color: $subject-main;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: $gap;

    .subject-head-block--title {
      text-decoration: unset;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .subject-book-block {
    display: flex;
    flex-wrap: wrap;
    gap: $gap;

    .single-subject-book {
      display: flex;
      gap: $gap;
      align-items: center;
      text-decoration: none;
      background-color: #fff;
      border-radius: 6px;
      color: black;
      border: 1px solid #d0d0d0;
      transition: all 0.3s ease;
      text-decoration: unset;
      padding: 2px;
      cursor: pointer;

      @media (max-width: $media-width) {
        width: 48%;
      }

      img {
        width: 40px;
        height: 40px;
      }
      span {
        font-size: 13px;
        max-width: 200px;

        @media (max-width: $media-width) {
          font-size: 11px;
        }
      }

      &:hover {
        transform: translateY(-2px);
        border: 1px solid #3e3e3e;

        span {
          text-decoration: underline;
        }
      }
      &.active {
        background-color: #45496a;
        color: white;
      }
    }
  }
}

// 加载更多触发器样式
.load-more-trigger {
  height: 20px;
  width: 100%;
}

// 图片懒加载优化样式
.lozad {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;

  &.loaded {
    opacity: 1;
  }
}
</style>
