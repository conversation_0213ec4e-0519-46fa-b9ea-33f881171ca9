<template>
  <div class="login-container">
    <h2>登录</h2>
    <form @submit.prevent="handleLogin">
      <div class="form-group">
        <label for="username">用户名</label>
        <input id="username" v-model="username" type="text" required />
      </div>
      <div class="form-group">
        <label for="password">密码</label>                          
        <input id="password" v-model="password" type="password" required />
      </div>
      <button type="submit">登录</button>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
// import { login } from '@/api/user'
import { useRouter } from 'vue-router'

const username = ref('')
const password = ref('')
const router = useRouter()

async function handleLogin(): Promise<void> {
  try {
    // const res = await login({
    //   username: username.value,
    //   password: password.value
    // })
    // if (res && res.code!==0) {
    //   alert('登录失败，账号或密码错误')
    //   return;
    // }

    // // 成功，设置
    // localStorage.setItem('isLogin', '1')
    // console.log('登录成功，重定向到首页')
    // 重定向
    router.push('/')
  } catch (err) {
    console.error('登录失败:', err)
    alert('登录失败，账号或密码错误')
  }
}
</script>

<style scoped>
.login-container {
  max-width: 320px;
  margin: 60px auto;
  padding: 32px;
  border: 1px solid #eee;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  background: #fff;
}
.form-group {
  margin-bottom: 18px;
}
label {
  display: block;
  margin-bottom: 6px;
  font-weight: bold;
}
input {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
  border: 1px solid #ccc;
  border-radius: 4px;
}
button {
  width: 100%;
  padding: 10px;
  background: #42b983;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}
button:hover {
  background: #369870;
}
</style> 