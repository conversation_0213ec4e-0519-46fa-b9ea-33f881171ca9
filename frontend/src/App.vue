<script setup lang="ts">
import { RouterView } from 'vue-router'
import AppNavbar from './components/AppNavbar.vue'
</script>

<template>
  <!-- 导航栏 -->
  <AppNavbar />

  <!-- 主体内容 -->
  <div class="main-container">
    <RouterView />
  </div>

  <!-- 跳到顶部/底部 -->
  <go-up-or-down />
</template>

<style>
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: 'Arial', 'Microsoft YaHei', sans-serif;
}

.main-container {
  margin-top: 60px;
}
</style>
