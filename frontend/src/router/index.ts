import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('@/views/Home.vue'),
    },
    {
      path: '/book/:book_id',
      name: 'book_details',
      component: () => import('@/views/BookDetails.vue')
    },
    {
      path: '/subjects',
      name: 'subjects',
      component: () => import('@/views/Subject.vue')
    },
    {
      path: '/subject/:subject_id',
      name: 'subject_details',
      component: () => import('@/views/SubjectDetails.vue')
    },
    {
      path: '/upload',
      name: 'upload',
      component: () => import('@/views/BookUpload.vue')
    }
  ],
})

export default router
