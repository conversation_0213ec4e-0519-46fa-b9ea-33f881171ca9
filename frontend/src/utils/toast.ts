import { createApp, h } from 'vue'
import type { App, ComponentPublicInstance } from 'vue'
import MyToast from '@/components/MyToast.vue'
import FontAwesomeIcon from '@/utils/fontawesome'

// 定义Toast实例类型
interface ToastInstance {
  success: (message: string, duration?: number) => number;
  warning: (message: string, duration?: number) => number;
  error: (message: string, duration?: number) => number;
  primary: (message: string, duration?: number) => number;
  remove: (id: number) => void;
  clear: () => void;
}

// 扩展ComponentPublicInstance接口，以支持Toast组件的方法
interface ToastComponentInstance extends ComponentPublicInstance {
  success?: (message: string, duration?: number) => number;
  warning?: (message: string, duration?: number) => number;
  error?: (message: string, duration?: number) => number;
  primary?: (message: string, duration?: number) => number;
  remove?: (id: number) => void;
  clear?: () => void;
}

// 单例模式保存toast实例
let toastInstance: ToastInstance | null = null
let app: App | null = null

// 创建DOM容器
function createToastContainer() {
  // 先检查是否已存在容器
  let container = document.getElementById('my-toast-container')

  // 如果不存在才创建
  if (!container) {
    container = document.createElement('div')
    container.id = 'my-toast-container'
    document.body.appendChild(container)
  }

  return container
}

// 确保toast实例存在
function ensureToast(): ToastInstance {
  if (!toastInstance) {
    const container = createToastContainer()

    // 如果已经有Vue应用挂载，先卸载
    if (app) {
      app.unmount()
      app = null
    }

    // 创建新的Vue应用
    app = createApp(MyToast)

    // 注册FontAwesomeIcon组件
    app.component('font-awesome-icon', FontAwesomeIcon)

    // 挂载
    const instance = app.mount(container) as ToastComponentInstance

    // 检查实例是否有所需方法
    if (typeof instance.success !== 'function' ||
      typeof instance.error !== 'function' ||
      typeof instance.warning !== 'function' ||
      typeof instance.primary !== 'function') {
      console.error('Toast方法缺失:', {
        success: typeof instance.success === 'function',
        error: typeof instance.error === 'function',
        warning: typeof instance.warning === 'function',
        primary: typeof instance.primary === 'function'
      })

      // 如果实例不完整，尝试重新创建更可靠的版本
      const fixedInstance: ToastInstance = {
        success: (message, duration) => {
          console.log('成功:', message)
          alert('成功: ' + message)
          return -1
        },
        error: (message, duration) => {
          console.log('错误:', message)
          alert('错误: ' + message)
          return -1
        },
        warning: (message, duration) => {
          console.log('警告:', message)
          alert('警告: ' + message)
          return -1
        },
        primary: (message, duration) => {
          console.log('提示:', message)
          alert('提示: ' + message)
          return -1
        },
        remove: () => { },
        clear: () => { }
      }

      // 使用后备实例
      toastInstance = fixedInstance
      return toastInstance
    }

    // 确保所有方法都存在后，将实例转换为 ToastInstance 类型
    toastInstance = {
      success: instance.success as ToastInstance['success'],
      error: instance.error as ToastInstance['error'],
      warning: instance.warning as ToastInstance['warning'],
      primary: instance.primary as ToastInstance['primary'],
      remove: instance.remove as ToastInstance['remove'],
      clear: instance.clear as ToastInstance['clear']
    }
  }

  return toastInstance
}

// 对外暴露的接口
export const Toast = {
  /**
   * 显示成功消息
   * @param message 消息内容
   * @param duration 显示时长(毫秒), 默认3000
   */
  success(message: string, duration?: number) {
    try {
      return ensureToast().success(message, duration)
    } catch (e) {
      console.error('Toast.success 失败:', e)
      alert(`成功: ${message}`)
      return -1
    }
  },

  /**
   * 显示警告消息
   * @param message 消息内容
   * @param duration 显示时长(毫秒), 默认3000
   */
  warning(message: string, duration?: number) {
    try {
      return ensureToast().warning(message, duration)
    } catch (e) {
      console.error('Toast.warning 失败:', e)
      alert(`警告: ${message}`)
      return -1
    }
  },

  /**
   * 显示错误消息
   * @param message 消息内容
   * @param duration 显示时长(毫秒), 默认3000
   */
  error(message: string, duration?: number) {
    try {
      return ensureToast().error(message, duration)
    } catch (e) {
      console.error('Toast.error 失败:', e)
      alert(`错误: ${message}`)
      return -1
    }
  },

  /**
   * 显示普通消息
   * @param message 消息内容
   * @param duration 显示时长(毫秒), 默认3000
   */
  primary(message: string, duration?: number) {
    try {
      return ensureToast().primary(message, duration)
    } catch (e) {
      console.error('Toast.primary 失败:', e)
      alert(`提示: ${message}`)
      return -1
    }
  },

  /**
   * 移除指定ID的消息
   * @param id 消息ID
   */
  remove(id: number) {
    try {
      if (toastInstance) {
        toastInstance.remove(id)
      }
    } catch (e) {
      console.error('Toast.remove 失败:', e)
    }
  },

  /**
   * 清除所有消息
   */
  clear() {
    try {
      if (toastInstance) {
        toastInstance.clear()
      }
    } catch (e) {
      console.error('Toast.clear 失败:', e)
    }
  }
}

// 添加清理方法
window.addEventListener('beforeunload', () => {
  if (app) {
    app.unmount()
    app = null
    toastInstance = null
    const container = document.getElementById('my-toast-container')
    if (container) {
      container.remove()
    }
  }
}) 