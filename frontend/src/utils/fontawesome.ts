import { library } from "@fortawesome/fontawesome-svg-core";
import {
    faAngleDown,
    faAnglesDown,
    faAnglesUp,
    faArrowUpWideShort,
    faBook,
    faCaretDown,
    faCaretUp,
    faCheck,
    faCheckCircle,
    faCirclePlus,
    faCircleXmark,
    faDownload,
    faEdit,
    faExclamationTriangle,
    faEye,
    faFilter,
    faGears, faHome,
    faImage,
    faInfoCircle,
    faLayerGroup,
    faMinus,
    faObjectGroup,
    faPlus,
    faRandom,
    faRefresh,
    faSearch,
    faStar,
    faTags,
    faTimesCircle,
    faTrashCan,
    faUndo,
    faUpload,
    faXmark,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

library.add(
    faBook, faStar, faRefresh, faUndo, faTags, faFilter, faRandom,
    faAnglesUp, faAnglesDown, faEdit, faCheckCircle, faCheck, faExclamationTriangle,
    faTimesCircle, faInfoCircle, faCirclePlus, faEye,
    faImage, faMinus, faPlus, faDownload, faTrashCan,
    faCircleXmark, faXmark, faGears, faHome, faSearch, faLayerGroup,
    faArrowUpWideShort, faObjectGroup, faAngleDown,
    faCaretUp, faCaretDown, faUpload,
);

// 使用方法
// <font-awesome-icon icon="book" />
// <font-awesome-icon icon="fa-solid fa-book" />
// <font-awesome-icon :icon="['fas', 'book']" />

export default FontAwesomeIcon;
