<template>
  <!-- 弹窗-修改-专题 -->
  <my-dialog title="修改专题" v-model="insideVisible" width="80%" @close="close">
    <div class="edit-subjects--show-block">
      <div
        class="single-subject"
        :class="{ active: liSubjectsIsChosing.some((tmp) => tmp.id === subject.id) }"
        v-for="subject in bookMetaStore.subjects"
        :key="subject.id"
        @click="toggleEditSubjectChose(subject)"
      >
        <img :src="subject.thumb_url" class="img" alt="" />
        <div class="title">{{ subject.title }}</div>
      </div>
    </div>

    <template #footer>
      <div class="my_flex_end">
        <my-button @click="close">取 消</my-button>
        <my-button type="primary" @click="submit">确 定</my-button>
      </div>
    </template>
  </my-dialog>
</template>

<script setup lang="ts">
import type { Subject } from '@/types/book'
import { defineEmits } from 'vue'

// 使用书籍元数据 store
const bookMetaStore = useBookMetaStore()

interface Props {
  visible: boolean
  bookMeta: { id: number | string; li_subjects: Subject[] }
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
})

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'on-close'): void
  (e: 'on-success'): void
}>()

// 内部 visibe
const insideVisible = ref(props.visible)

// 已选中
const liSubjectsIsChosing = ref<Subject[]>([])

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 如果是打开，则从父组件值，初始化 已选中 tags
      liSubjectsIsChosing.value = [...props.bookMeta.li_subjects]

      // 加载 专题-列表 到缓存
      bookMetaStore.fetchSubjects()
    }

    insideVisible.value = newVal
  },
)

/**
 * 切换-选中专题
 */
const toggleEditSubjectChose = (subject: Subject) => {
  if (liSubjectsIsChosing.value.some((tmp) => tmp.id === subject.id)) {
    liSubjectsIsChosing.value = liSubjectsIsChosing.value.filter((tmp) => tmp.id !== subject.id)
  } else {
    liSubjectsIsChosing.value.push(subject)
  }
}

/**
 * 内部 关闭
 */
const close = () => {
  insideVisible.value = false
  emit('on-close')
}

/**
 * 提交
 */
const submit = async () => {
  if (liSubjectsIsChosing.value.length === 0) {
    Toast.error('请至少选择一个专题')
    return
  }

  try {
    // 提交
    await editBookSubjectsApi(
      props.bookMeta.id,
      liSubjectsIsChosing.value.map((item) => item.id),
    )
    Toast.success('修改成功')
    close()
    emit('on-success')

    // 刷新 subject 列表 缓存
    bookMetaStore.fetchSubjects(true)
  } catch (error) {
    Toast.error('修改失败')
  }
}
</script>

<style lang="scss" scoped>
.edit-subjects--show-block {
  display: flex;
  flex-wrap: wrap;
  gap: $gap * 2;

  @media (max-width: $media-width) {
    gap: $gap;
  }

  .single-subject {
    border: 1px solid #b9b9b9;
    border-radius: 4px;
    background-color: #f5f5f5;
    cursor: pointer;
    padding: 2px;

    .img {
      width: 120px;
      height: 120px;
      object-fit: cover;

      @media (max-width: $media-width) {
        width: 60px;
        height: 60px;
      }
    }

    .title {
      font-size: 12px;
      text-align: center;
      color: #666;
      // padding: 2px;
    }

    &:hover {
      border: 1px solid #838383;
    }

    &.active {
      background: #4a90e2;
    }

    &.active .title {
      color: white;
    }
  }
}
</style>
