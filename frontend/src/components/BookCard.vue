<template>
  <div class="book-card" :title="book.title">
    <!-- 专题-显示（hover才显示） -->
    <div class="show-subjects">
      <div v-for="subject in book.li_subjects">
        <font-awesome-icon icon="fa-solid fa-layer-group" />
        <span>{{ subject.title }}</span>
      </div>
      <my-button
        type="primary"
        size="mini"
        @click="() => emit('ready-to-edit-subjects', props.book)"
      >
        <font-awesome-icon icon="fa-solid fa-edit" />
      </my-button>
    </div>

    <div class="cover-container" v-show="!hideImg">
      <router-link :to="bookDetailUrl">
        <img
          src="@/assets/img/blank.png"
          :data-src="book.first_img"
          :alt="book.title"
          class="cover-image lozad"
        />
      </router-link>

      <!-- 删除按钮 -->
      <my-button
        type="danger"
        class="del-btn"
        size="small"
        circle
        v-if="delBtnVisible"
        @click="() => emit('handle-delete', props.book)"
      >
        <font-awesome-icon icon="fa-solid fa-trash-can" />
      </my-button>

      <!-- 收藏按钮 -->
      <div class="collect-btn" @click="(e) => toggleCollect(e)">
        <font-awesome-icon icon="fa-solid fa-star" :class="{ active: book.is_collect }" />
      </div>

      <!-- 书本数量 -->
      <div class="book-page-count">
        <font-awesome-icon icon="fa-solid fa-image" />
        <span>{{ book.page_count }}</span>
      </div>
    </div>
    <div class="book-info">
      <!-- 标题 -->
      <router-link class="book-title" :to="bookDetailUrl">{{ book.title }}</router-link>

      <!-- 类型 -->
      <div class="book-genre" v-if="!props.isSimple">
        <div class="book-genre-text" @click="handleGenreClick(book.genre)">
          {{ book.genre || '&nbsp' }}
        </div>
        <!-- 编辑按钮 -->
        <div class="edit-btn" @click="() => emit('ready-to-edit-genre', props.book)">
          <font-awesome-icon icon="fa-solid fa-edit" />
        </div>
      </div>

      <!-- 标签 -->
      <div class="book-tags-block" v-if="!props.isSimple">
        <div class="book-tags">
          <div
            v-if="hasTags"
            v-for="tag in book.tags"
            :key="tag.id"
            class="tag"
            @click="handleTagClick(tag.value)"
          >
            {{ tag.value }}
          </div>
          <div v-else>&nbsp;</div>
        </div>

        <!-- 编辑按钮 -->
        <div class="edit-btn" @click="() => emit('ready-to-edit-tags', props.book)">
          <font-awesome-icon icon="fa-solid fa-edit" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Book } from '@/types/book';
import lozad from 'lozad';
import { defineEmits } from 'vue';

// 定义 props 的类型
const props = withDefaults(
  defineProps<{
    book: Book
    hideImg?: boolean // 可选
    isSimple?: boolean // 可选
    noJump?: boolean // 可选
    delBtnVisible?: boolean // 删除按钮
  }>(),
  {
    hideImg: false, // 设置默认值
    isSimple: false, // 简单显示模式
    noJump: false, // 点击不进行跳转
    delBtnVisible: false, // 删除按钮
  },
)

const emit = defineEmits<{
  (e: 'chose-genre', genre: string): void
  (e: 'chose-tag', tag: string): void
  (e: 'refresh-book'): void
  (e: 'ready-to-edit-tags', book: Book): void
  (e: 'ready-to-edit-genre', book: Book): void
  (e: 'ready-to-edit-subjects', book: Book): void
  (e: 'handle-delete', book: Book): void
}>()

// 计算属性
const bookDetailUrl = computed(() => (props.noJump ? '' : `/book/${props.book.id}`))
const hasTags = computed(() => props.book.tags && props.book.tags.length > 0)

/**
 * 点击类型
 * @param val 类型
 */
const handleGenreClick = (val: string | undefined) => {
  if (!val) return

  emit('chose-genre', val)
}

/**
 * 点击标签
 * @param val 标签
 */
const handleTagClick = (val: string) => {
  emit('chose-tag', val)
}

// 收藏操作防抖
const isCollecting = ref(false)

/**
 * 点击收藏
 * @param event 事件
 */
const toggleCollect = async (event: Event) => {
  // 阻止事件冒泡，避免触发卡片点击事件
  event.stopPropagation()

  // 防止重复点击
  if (isCollecting.value) return
  isCollecting.value = true

  try {
    const res = await toggleCollectApi(props.book.id)

    Toast.success(`${res === 1 ? '收藏' : '取消收藏'}成功`)

    // 通知父组件 刷新
    emit('refresh-book')
  } catch (error) {
    Toast.error(error as string)
  } finally {
    isCollecting.value = false
  }
}

// on mounted
onMounted(async () => {
  const observer = lozad() // lazy loads elements with default selector as '.lozad'
  observer.observe()
})
</script>

<style scoped lang="scss">
.book-card {
  width: 160px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  box-sizing: border-box;
  position: relative;
  border: 1px solid #eeeeee;

  @media (max-width: $media-width) {
    width: unset;
  }

  &:hover {
    box-shadow: 0 5px 16px rgba(0, 0, 0, 0.2);
    border: 1px solid #b3b3b3;
    transform: scale(1.05); /* 鼠标悬停时放大效果 */

    .show-subjects {
      visibility: visible;
    }
  }

  .show-subjects {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    padding: 2px;

    background-color: #161616;
    color: white;
    font-size: 12px;
    opacity: 0.8;
    visibility: hidden;
    min-width: 50px;
  }

  .cover-container {
    border-bottom: 1px solid #cdcdcd;
    cursor: pointer;
    position: relative;

    .cover-image {
      width: 100%;
      height: 180px;
      object-fit: cover;

      // 移动端适配
      @media (max-width: $media-width) {
        width: 120px;
        height: 120px;
      }
    }

    .book-page-count {
      @include flex-start;
      font-size: 12px;
      color: #999;
      position: absolute;
      top: $gap * 4;
      right: 8px;
      padding: 2px;
    }

    // 删除按钮
    .del-btn {
      position: absolute;
      right: $gap;
      bottom: $gap;
    }

    /* 收藏按钮样式 */
    .collect-btn {
      position: absolute;
      top: 4px;
      right: 4px;
      width: 20px;
      height: 20px;
      background-color: #f7f7f7;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      z-index: 2;

      @media (max-width: $media-width) {
        top: 2px;
        right: 2px;
      }

      &:hover {
        transform: scale(1.1);

        .fa-star {
          color: #ffbab5;
        }
      }

      .fa-star {
        color: #aaaaaa;
        font-size: 13px;
        transition: color 0.2s ease;

        &.active {
          color: #f86055;
        }
      }
    }

    &:hover ~ .book-info .book-title {
      text-decoration: underline;
    }
  }

  .book-info {
    padding: 4px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;

    @media (max-width: $media-width) {
      max-width: 120px;
    }

    .book-title {
      flex: 1;
      margin: 0;
      font-size: 13px;
      font-weight: 600;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      height: 18px;
      width: 100%;
      text-decoration: unset;
      color: unset;

      &:hover {
        text-decoration: underline;
      }
    }

    .book-genre {
      width: 100%;
      height: 16px;
      @include flex-between;

      .book-genre-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #df660e; /* 橙色 */
        display: inline-block;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }

      .edit-btn {
        font-size: 12px;
        visibility: hidden;
        cursor: pointer;
        color: $blue-light;
      }

      &:hover .edit-btn {
        visibility: visible;
      }
    }

    .book-tags-block {
      width: 100%;
      @include flex-between;

      .edit-btn {
        font-size: 12px;
        visibility: hidden;
        cursor: pointer;
        color: $blue-light;
      }

      &:hover .edit-btn {
        visibility: visible;
      }
    }

    .book-tags {
      display: flex;
      justify-content: flex-start;
      gap: 4px;
      overflow-x: auto;
      scrollbar-width: none;
      height: 16px;
    }

    .tag {
      padding: 0px 4px;
      background-color: #28a745;
      color: white;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s ease;
      white-space: nowrap;
      flex-shrink: 0;
      @include flex-center;
    }
  }
}
</style>
