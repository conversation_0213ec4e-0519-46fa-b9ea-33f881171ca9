<template>
  <label class="checkbox-container">
    <input type="checkbox" v-model="innerModel" />
    <span class="text">
      <slot>{{ label }}</slot>
    </span>
  </label>
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: [<PERSON>olean, Number, String],
    default: false,
  },
  label: {
    type: String,
    default: '',
  },
  trueValue: {
    type: [Boolean, Number, String],
    default: true,
  },
  falseValue: {
    type: [Boolean, Number, String],
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

// 内部模型，用于双向绑定
const innerModel = computed({
  get() {
    return props.modelValue === props.trueValue
  },
  set(val) {
    // 内部 勾选/取消 触发
    emit('update:modelValue', val ? props.trueValue : props.falseValue)
  },
})
</script>

<style scoped lang="scss">
.checkbox-container {
  border: 1px solid #c0c0c0;
  border-radius: 4px;
  padding: 6px 8px;
  background-color: white;

  font-size: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #666666;

  .text {
    margin-left: 4px;
  }
}
</style>
