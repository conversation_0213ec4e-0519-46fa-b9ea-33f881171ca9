<template>
  <div class="btn-container">
    <font-awesome-icon class="item" icon="fa-solid fa-caret-up" @click="goTop"></font-awesome-icon>

    <font-awesome-icon
      class="item"
      icon="fa-solid fa-caret-down"
      @click="goDown"
    ></font-awesome-icon>
  </div>
</template>

<script setup>
const goTop = () => {
  window.scrollTo(0, 0)
}

const goDown = () => {
  window.scrollTo(0, document.body.scrollHeight)
}
</script>

<style lang="scss" scope>
.btn-container {
  position: fixed;
  right: $gap;
  bottom: $gap * 10;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: $gap;

  .item {
    width: 20px;
    height: 20px;
    background-color: $green;
    color: white;
    border-radius: 50%;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
    &:active {
      transform: translateY(1px);
    }
  }
}
</style>
