<template>
  <!-- 弹窗-修改-类型 -->
  <my-dialog title="修改 类型" v-model="insideVisible" :width="700" @close="close">
    <div class="genre-show-block">
      <div
        class="single-genre"
        :class="{ active: props.bookMeta.genre === genre.value }"
        v-for="genre in bookMetaStore.genres"
        :key="genre.id"
        @click="submit(genre.value)"
      >
        {{ genre.value }}
      </div>
    </div>
  </my-dialog>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue';

// 使用书籍元数据 store
const bookMetaStore = useBookMetaStore()

interface Props {
  visible: boolean
  bookMeta: { id: number | string; genre: string }
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
})

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'on-close'): void
  (e: 'on-success'): void
}>()

// 内部 visibe
const insideVisible = ref(props.visible)

watch(
  () => props.visible,
  (newVal) => {
    insideVisible.value = newVal
  },
)

/**
 * 内部 关闭事件
 */
const close = () => {
  insideVisible.value = false // 内部关闭

  emit('on-close') // 通知外部关闭
}

const submit = async (genre: string) => {
  try {
    await editGenreApi(props.bookMeta.id, genre)
    Toast.success('修改成功')

    // 关闭
    close()

    // 刷新
    emit('on-success')
  } catch (error) {
    Toast.error('修改失败', 3000) // 3秒后自动关闭
    return
  }
}

onMounted(() => {
  bookMetaStore.fetchGenres()
})
</script>

<style lang="scss" scoped>
.genre-show-block {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
  padding: 4px;

  @media (max-width: $media-width) {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: $gap;
  }

  .single-genre {
    // @include flex-center;
    padding: 8px 12px;
    background: #f5f5f5;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    @media (max-width: $media-width) {
      padding: 4px;
      font-size: 11px;
    }

    &:hover {
      background-color: #e8f3ff;
      color: #4a90e2;
    }

    &.active {
      background-color: #4a90e2;
      color: white;
    }
  }
}
</style>
