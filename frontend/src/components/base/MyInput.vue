<template>
  <div class="my-input-wrapper">
    <!-- 前置插槽 -->
    <div v-if="$slots.prefix" class="my-input__prefix">
      <slot name="prefix"></slot>
    </div>

    <input
      :type="type"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :class="[
        'my-input',
        'my-input--normal',
        {
          'my-input--disabled': disabled,
          'my-input--with-prefix': $slots.prefix,
        },
      ]"
      @input="handleInput"
      @focus="$emit('focus', $event)"
      @blur="$emit('blur', $event)"
      @keydown="handleKeydown"
      @keyup="handleKeyup"
    />
    <button
      v-if="showClearButton"
      type="button"
      :class="['my-input__clear', 'my-input__clear--normal']"
      @click="handleClear"
      @mousedown.prevent
    >
      <font-awesome-icon icon="fa-solid fa-circle-xmark"></font-awesome-icon>
    </button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue?: string | number
  type?: 'text' | 'number' | 'password'
  placeholder?: string
  disabled?: boolean
  clear?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string | number): void
  (e: 'focus', event: FocusEvent): void
  (e: 'blur', event: FocusEvent): void
  (e: 'clear'): void
  (e: 'keyup', event: KeyboardEvent): void
  (e: 'keyup.enter', event: KeyboardEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  type: 'text',
  placeholder: '',
  disabled: false,
  clear: false,
})

const emit = defineEmits<Emits>()

// 优化：简化计算属性，减少不必要的计算
const showClearButton = computed(() => {
  if (!props.clear || props.disabled) return false

  if (props.type === 'number') {
    return props.modelValue !== 0 && props.modelValue !== ''
  }
  return props.modelValue !== '' && props.modelValue !== undefined
})

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value

  if (props.type === 'number') {
    // 优化：简化数字处理逻辑
    const cleanValue = value.replace(/[^0-9.-]/g, '')
    const parts = cleanValue.split('.')
    const finalValue = parts.length > 2 ? parts[0] + '.' + parts.slice(1).join('') : cleanValue
    emit('update:modelValue', parseFloat(finalValue) || 0)
  } else {
    emit('update:modelValue', value)
  }
}

const handleKeydown = (event: KeyboardEvent) => {
  if (props.type !== 'number') return

  // 优化：使用Set提升查找性能
  const allowedKeys = new Set([
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '.',
    '-',
    'Backspace',
    'Delete',
    'ArrowLeft',
    'ArrowRight',
    'ArrowUp',
    'ArrowDown',
  ])

  if (!allowedKeys.has(event.key)) {
    event.preventDefault()
    return
  }

  const target = event.target as HTMLInputElement
  const value = target.value

  // 防止多个小数点或负号
  if ((event.key === '.' && value.includes('.')) || (event.key === '-' && value.includes('-'))) {
    event.preventDefault()
  }
}

const handleKeyup = (event: KeyboardEvent) => {
  // 触发通用的 keyup 事件
  emit('keyup', event)
  console.log('keyup')

  // 触发特定按键的事件
  if (event.key === 'Enter') {
    console.log('触发特定按键的事件')
    emit('keyup.enter', event)
  }
}

const handleClear = () => {
  const defaultValue = props.type === 'number' ? 0 : ''
  emit('update:modelValue', defaultValue)
  emit('clear')
}
</script>

<style scoped lang="scss">
$base-height: 32px;

.my-input-wrapper {
  position: relative;
  width: 100%;
  padding: 0 4px;
  height: $base-height;
}

.my-input {
  height: $base-height;
  width: 100%;
  line-height: 1.5;
  color: #333;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition:
    border-color 0.3s ease,
    box-shadow 0.3s ease;
  box-sizing: border-box;
  outline: none;
}

.my-input--normal {
  padding: 6px 12px;
  padding-right: 36px;
  font-size: 13px;

  &.my-input--with-prefix {
    padding-left: 28px;
  }
}

.my-input:hover {
  border-color: #40a9ff;
}

.my-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.my-input::placeholder {
  color: #bfbfbf;
}

.my-input--normal::placeholder {
  font-size: 13px;
}

.my-input--disabled {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
  cursor: not-allowed;
}

.my-input--disabled:hover,
.my-input--disabled:focus {
  border-color: #d9d9d9;
  box-shadow: none;
}

.my-input__prefix {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  display: flex;
  align-items: center;
  height: 100%;
  color: #848484;
  pointer-events: none; /* 防止干扰输入框交互 */
  left: 10px;
}

/* 前缀插槽尺寸样式 */
.my-input--normal + .my-input__prefix {
  left: 12px;
  font-size: 13px;
  width: 20px;
  justify-content: center;
}

.my-input__clear {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: #bfbfbf;
  opacity: 0;
  visibility: hidden;
}

/* 清空按钮尺寸样式 */
.my-input__clear--normal {
  right: 7px;
  padding: 3px;
  font-size: 15px;
}

.my-input-wrapper:hover .my-input__clear {
  opacity: 1;
  visibility: visible;
}

.my-input__clear:hover {
  background-color: #f0f0f0;
  color: #666;
}

.my-input__clear:active {
  background-color: #e6e6e6;
  color: #333;
}

.my-input[type='number'] {
  -moz-appearance: textfield;
}

.my-input[type='number']::-webkit-outer-spin-button,
.my-input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
