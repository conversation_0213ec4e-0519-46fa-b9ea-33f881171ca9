<template>
  <div v-if="props.visible" class="overlay" @click="handleClose">
    <img :src="props.previewImage" class="preview-image" @click.stop />
  </div>
</template>

<script setup>
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  previewImage: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:visible'])

/**
 * 关闭
 */
const handleClose = () => {
  emit('update:visible', false)
}

// 监听Esc关闭
const handleEsc = (e) => {
  if (props.visible && e.key === 'Escape') {
    handleClose()
  }
}

onMounted(() => {
  window.addEventListener('keydown', handleEsc)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleEsc)
})
</script>

<style lang="scss" scope>
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9050;
}

.preview-image {
  max-width: 90%;
  max-height: 90%;

  @media (max-width: $media-width) {
    max-width: 99%;
    max-height: 99%;
  }
}
</style>
