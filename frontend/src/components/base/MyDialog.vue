<template>
  <teleport to="body">
    <!-- 遮罩层 -->
    <div v-if="visible" class="my-dialog-mask" @click="handleMaskClick"></div>

    <!-- 对话框主体 -->
    <div v-if="visible" class="my-dialog" :style="{ width: dialogWidth }">
      <!-- 标题 -->
      <div class="my-dialog-header">
        <slot name="title">{{ title }}</slot>
        <button v-if="showClose" class="my-dialog-close" @click="handleClose">×</button>
      </div>

      <!-- 内容 -->
      <div class="my-dialog-body">
        <slot></slot>
      </div>

      <!-- 底部按钮 -->
      <div v-if="$slots.footer" class="my-dialog-footer">
        <slot name="footer"></slot>
      </div>
    </div>
  </teleport>
</template>

<script setup>
const props = defineProps({
  // 是否显示对话框
  modelValue: {
    type: <PERSON>olean,
    default: false,
  },
  // 对话框标题
  title: {
    type: String,
    default: '提示',
  },
  // 对话框宽度
  width: {
    type: [String, Number],
    default: '50%',
  },
  // 是否显示关闭按钮
  showClose: {
    type: Boolean,
    default: true,
  },
  // 点击遮罩层是否可以关闭
  closeOnClickModal: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue', 'close'])

const visible = ref(false)

const screenWidth = ref(window.innerWidth)

const isMobile = computed(() => screenWidth.value < 768)

/**
 * 更新屏幕宽度
 */
const updateWidth = () => {
  screenWidth.value = window.innerWidth
}

// 计算实际宽度
const dialogWidth = computed(() => {
  if (isMobile.value) return '85%'

  return typeof props.width === 'number' ? `${props.width}px` : props.width
})

// 同步外部modelValue变化
watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
  },
  { immediate: true },
)

// 关闭对话框
const handleClose = () => {
  visible.value = false
  emit('update:modelValue', false)
  emit('close')
}

// 点击遮罩层
const handleMaskClick = () => {
  if (props.closeOnClickModal) {
    handleClose()
  }
}

// 监听Esc关闭
const handleEsc = (e) => {
  if (visible.value && e.key === 'Escape') {
    handleClose()
  }
}

onMounted(() => {
  // 监听 宽度
  window.addEventListener('resize', updateWidth)

  window.addEventListener('keydown', handleEsc)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateWidth)

  window.removeEventListener('keydown', handleEsc)
})
</script>

<style scoped lang="scss">
.my-dialog-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9000;
  background-color: rgba(0, 0, 0, 0.5);
}

.my-dialog {
  position: fixed;
  top: 100px;
  left: 50%;
  /* transform: translate(-50%, -50%); */
  transform: translateX(-50%);
  z-index: 9999;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

  @media (max-width: $media-width) {
    top: 80px;
  }

  .my-dialog-body {
    padding: 20px;
    max-height: 400px;
    overflow: auto;

    @media (max-width: $media-width) {
      padding: $gap;
    }
  }

  .my-dialog-footer {
    padding: 16px;
    border-top: 1px solid #eee;
  }
}

.my-dialog-header {
  padding: 16px;
  font-size: 18px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.my-dialog-title {
  font-size: 16px;
  font-weight: bold;
}

.my-dialog-close {
  border: none;
  background: transparent;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

.my-dialog-close:hover {
  color: #333;
}
</style>
