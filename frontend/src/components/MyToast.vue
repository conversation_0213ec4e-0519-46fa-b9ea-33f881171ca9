<template>
  <transition-group tag="div" name="toast-animation" class="my-toast-container">
    <div v-for="toast in toasts" :key="toast.id" :class="['my-toast', `my-toast--${toast.type}`]">
      <div class="my-toast-icon">
        <font-awesome-icon :icon="getIconClass(toast.type)" />
      </div>
      <div class="my-toast-content">{{ toast.message }}</div>
    </div>
  </transition-group>
</template>

<script lang="ts" setup>
// 存储当前显示的所有消息
const toasts = ref<
  {
    id: number
    message: string
    type: 'success' | 'warning' | 'error' | 'primary'
    timer: number
  }[]
>([])

// 生成唯一ID
let nextId = 0

// 根据类型返回对应的图标类
const getIconClass = (type: string) => {
  const iconMap = {
    success: 'fa-solid fa-check-circle',
    warning: 'fa-solid fa-exclamation-triangle',
    error: 'fa-solid fa-times-circle',
    primary: 'fa-solid fa-info-circle',
  }
  return iconMap[type as keyof typeof iconMap] || iconMap.primary
}

// 添加一条消息
const add = (
  message: string,
  type: 'success' | 'warning' | 'error' | 'primary' = 'primary',
  duration = 3000,
) => {
  const id = nextId++

  // 添加到队列
  toasts.value.push({
    id,
    message,
    type,
    timer: window.setTimeout(() => {
      // 时间到后自动移除
      remove(id)
    }, duration),
  })

  return id
}

// 移除指定ID的消息
const remove = (id: number) => {
  const index = toasts.value.findIndex((toast) => toast.id === id)
  if (index !== -1) {
    // 清除定时器
    clearTimeout(toasts.value[index].timer)
    // 移除元素
    toasts.value.splice(index, 1)
  }
}

// 清空所有消息
const clear = () => {
  toasts.value.forEach((toast) => {
    clearTimeout(toast.timer)
  })
  toasts.value = []
}

// 暴露方法
defineExpose({
  success: (message: string, duration?: number) => add(message, 'success', duration),
  warning: (message: string, duration?: number) => add(message, 'warning', duration),
  error: (message: string, duration?: number) => add(message, 'error', duration),
  primary: (message: string, duration?: number) => add(message, 'primary', duration),
  remove,
  clear,
})

// 监听Esc关闭所有toast
const handleEsc = (e: KeyboardEvent) => {
  if (e.key === 'Escape') {
    clear()
  }
}

onMounted(() => {
  window.addEventListener('keydown', handleEsc)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleEsc)
})
</script>

<style scoped lang="scss">
.my-toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
  pointer-events: none;
}

.my-toast {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 4px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
  color: white;
  pointer-events: auto;

  &--success {
    // background-color: #67c23a;
    background-color: #f0f9eb;
    color: #67c23a;
  }

  &--warning {
    background-color: #fdf6ec;
    color: #e6a23c;
  }

  &--error {
    background-color: #fef0f0;
    color: #f56c6c;
  }
  &--primary {
    background-color: #409eff;
  }
}

.my-toast-icon {
  margin-right: 12px;
  font-size: 18px;
}

.my-toast-content {
  font-size: 14px;
  line-height: 1.4;
}

// 动画效果
.toast-animation-enter-active,
.toast-animation-leave-active {
  transition: all 0.3s ease;
}

.toast-animation-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.toast-animation-leave-to {
  transform: translateY(-30px);
  opacity: 0;
}
</style>
