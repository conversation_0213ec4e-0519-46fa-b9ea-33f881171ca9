/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppNavbar: typeof import('./components/AppNavbar.vue')['default']
    BookCard: typeof import('./components/BookCard.vue')['default']
    BookDetails: typeof import('./views/BookDetails.vue')['default']
    BookUpload: typeof import('./views/BookUpload.vue')['default']
    EditGenreDialog: typeof import('./components/EditGenreDialog.vue')['default']
    EditSubjectsDialog: typeof import('./components/EditSubjectsDialog.vue')['default']
    EditTagsDialog: typeof import('./components/EditTagsDialog.vue')['default']
    FilterTagsSelector: typeof import('./components/FilterTagsSelector.vue')['default']
    GoUpOrDown: typeof import('./components/GoUpOrDown.vue')['default']
    Home: typeof import('./views/Home.vue')['default']
    Login: typeof import('./views/Login.vue')['default']
    MyButton: typeof import('./components/base/MyButton.vue')['default']
    MyCheckbox: typeof import('./components/MyCheckbox.vue')['default']
    MyDialog: typeof import('./components/base/MyDialog.vue')['default']
    MyDivider: typeof import('./components/base/MyDivider.vue')['default']
    MyInput: typeof import('./components/base/MyInput.vue')['default']
    MyPreview: typeof import('./components/base/MyPreview.vue')['default']
    MySelect: typeof import('./components/base/MySelect.vue')['default']
    MyToast: typeof import('./components/MyToast.vue')['default']
    MyUpload: typeof import('./components/base/MyUpload.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Subject: typeof import('./views/Subject.vue')['default']
    SubjectDetails: typeof import('./views/SubjectDetails.vue')['default']
  }
}
