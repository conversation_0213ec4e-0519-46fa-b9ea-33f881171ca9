/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const Book: typeof import('@/types/book')['Book']
  const EffectScope: typeof import('vue')['EffectScope']
  const Subject: typeof import('@/types/book')['Subject']
  const Tag: typeof import('@/types/book')['Tag']
  const Toast: typeof import('@/utils/toast')['Toast']
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const addSubjectApi: typeof import('@/api/subjects')['addSubjectApi']
  const addTagApi: typeof import('@/api/book')['addTagApi']
  const bookMetaStore: typeof import('@/stores')['bookMetaStore']
  const bookPagesSortApi: typeof import('@/api/book')['bookPagesSortApi']
  const combineBooksApi: typeof import('@/api/book')['combineBooksApi']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const createPinia: typeof import('pinia')['createPinia']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const delBookApi: typeof import('@/api/book')['delBookApi']
  const delBookPageApi: typeof import('@/api/book')['delBookPageApi']
  const delSubjectBookApi: typeof import('@/api/subjects')['delSubjectBookApi']
  const downloadBookApi: typeof import('@/api/book')['downloadBookApi']
  const editBookSubjectsApi: typeof import('@/api/book')['editBookSubjectsApi']
  const editBookTagsApi: typeof import('@/api/book')['editBookTagsApi']
  const editBookTitleApi: typeof import('@/api/book')['editBookTitleApi']
  const editGenreApi: typeof import('@/api/book')['editGenreApi']
  const effectScope: typeof import('vue')['effectScope']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getBookDetailsApi: typeof import('@/api/book')['getBookDetailsApi']
  const getBookListApi: typeof import('@/api/book')['getBookListApi']
  const getBookPagesListApi: typeof import('@/api/book')['getBookPagesListApi']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getGenresListApi: typeof import('@/api/book')['getGenresListApi']
  const getSubjectDetailsApi: typeof import('@/api/subjects')['getSubjectDetailsApi']
  const getSubjectsListApi: typeof import('@/api/subjects')['getSubjectsListApi']
  const getTagsListApi: typeof import('@/api/book')['getTagsListApi']
  const h: typeof import('vue')['h']
  const inject: typeof import('vue')['inject']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const sortSubjectsApi: typeof import('@/api/subjects')['sortSubjectsApi']
  const sortSubjectsBooksApi: typeof import('@/api/subjects')['sortSubjectsBooksApi']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const toggleCollectApi: typeof import('@/api/book')['toggleCollectApi']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const uploadBooksApi: typeof import('@/api/book')['uploadBooksApi']
  const useAttrs: typeof import('vue')['useAttrs']
  const useBookMetaStore: typeof import('@/stores/bookMetaStore')['useBookMetaStore']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useId: typeof import('vue')['useId']
  const useLink: typeof import('vue-router')['useLink']
  const useModel: typeof import('vue')['useModel']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
}
