import { fileURLToPath, URL } from 'node:url'

import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'
import vueDevTools from 'vite-plugin-vue-devtools'

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    // unplugin-auto-import: 自动导入函数和 API（<script> 中使用）
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        'pinia',
        // 自定义工具
        {
          // store
          '@/stores/bookMetaStore': ['useBookMetaStore'],
          // 消息工具 toast
          '@/utils/toast': ['Toast'],
          // book api
          '@/api/book': [
            'uploadBooksApi',
            'downloadBookApi',
            'combineBooksApi',
            'getGenresListApi',
            'getTagsListApi',
            'getBookListApi',
            'getBookPagesListApi',
            'toggleCollectApi',
            'editGenreApi',
            'editBookTagsApi',
            'editBookSubjectsApi',
            'editBookTitleApi',
            'addTagApi',
            'getBookDetailsApi',
            'delBookApi',
            'delBookPageApi',
            'bookPagesSortApi',
          ],
          // subject api
          '@/api/subjects': [
            'getSubjectsListApi',
            'addSubjectApi',
            'sortSubjectsApi',
            'getSubjectDetailsApi',
            'sortSubjectsBooksApi',
            'delSubjectBookApi',
          ]
        },

      ],
      dts: 'src/auto-imports.d.ts',
    }),
    // unplugin-vue-components: 自动导入 Vue 组件（<template> 中使用）
    Components({
      dirs: [
        'src/components',
        'src/components/base',
        'src/views'
      ],
      dts: 'src/components.d.ts',
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 使用 as * 使得变量无需前缀即可全局使用
        additionalData: `@use "@/assets/scss/variable.scss" as *;@use "@/assets/scss/global.scss" as *;`
      }
    }
  },
  server: {
    host: '0.0.0.0',
    port: 5175, // 运行端口
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:12345',
        changeOrigin: true,
      },
      // 解决开发环境中，backend 的 media 文件为相对路径，无法加载的问题
      '/media': {
        target: 'http://127.0.0.1:12345',
        changeOrigin: true,
      }
    }
  },
})
