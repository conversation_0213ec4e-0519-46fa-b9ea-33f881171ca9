# 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 yarn.lock
# 先复制依赖文件，利用 Docker 缓存机制
COPY package.json yarn.lock ./

# 安装依赖
# --frozen-lockfile 确保使用 yarn.lock 中的精确版本
RUN yarn install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
# 将 Vue 项目编译为静态文件
RUN yarn build

# 只保留打包产物    todo：为什么要 from 2次，不能直接找到对应路径dist吗？
FROM node:18-alpine AS prod

WORKDIR /app


# 复制构建产物到 nginx 目录
# 从构建阶段复制 dist 文件夹到 nginx 容器下的目录
COPY --from=builder /app/dist /app/dist