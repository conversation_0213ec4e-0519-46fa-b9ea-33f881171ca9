def send(
        code: int = 0,
        msg: str = '',
        data: None = None
) -> dict:

    return {
        'code': code,
        'msg': msg,
        'data': data
    }


# 常用宏定义
# ================== add ==================
ADD_SUCCESS = send(0, '添加成功', True)
ADD_ERROR_OF_REPEAT = send(1, '已存在，请勿重复添加', False)
ADD_EXCEPTION = send(1, '添加异常', False)

# ================== delete ==================
DEL_SUCCESS = send(0, '删除成功', True)
DEL_ERROR_OF_ID_NOT_EXISTING = send(1, '删除错误，id不存在', False)
DEL_EXCEPTION = send(1, '删除异常', False)

# ================== update ==================
UPDATE_SUCCESS = send(0, '修改成功', True)
UPDATE_ERROR_OF_REPEAT = send(1, '已存在，请勿重复修改', False)
UPDATE_ERROR_OF_ID_NOT_EXISTING = send(1, '修改错误，id不存在', False)
UPDATE_EXCEPTION = send(1, '修改异常', False)

# ================== query ==================
QUERY_EXCEPTION = send(1, '查询异常', False)
QUERY_ERROR_OF_ID_NOT_EXISTING = send(1, '查询错误，id不存在', False)

# ================== error ==================
ERROR_OF_ILLEGAL_PARAM = send(1, '错误，参数非法', False)
ERROR_OF_PARAMS_NULL = send(1, '错误，参数为空', False)
ERROR_OF_FILE_NULL = send(1, '错误，文件为空', False)
ERROR_OF_ID_NOT_EXISTING = send(1, '错误，id不存在', False)
