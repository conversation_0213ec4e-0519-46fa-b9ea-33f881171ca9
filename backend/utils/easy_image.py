import io
import os
import shutil

import requests
from PIL import Image

F_WEBP = '.webp'


class EasyImage:
    """图片工具"""

    def webp_2_jpg(self, webp_file_path: str = None, remove_origin=False):
        """webp转jpg"""
        img = Image.open(webp_file_path)
        img.load()

        file_name = webp_file_path.replace(F_WEBP, '')

        file_path = f'{file_name}.jpg'

        # 检查图像模式
        if img.mode == 'RGBA':
            # 将 RGBA 转换为 RGB（去掉 alpha 通道）
            img = img.convert('RGB')

        img.save(file_path)

        if remove_origin:
            os.remove(webp_file_path)

        return file_path

    def webp_2_jpg_in_dir(self, dir_path: str = None, remove_origin=False, show_print=True):
        """将文件夹中的 webp 全部转为jpg"""
        if not os.path.exists(dir_path):
            raise Exception('文件夹不存在')

        for filename in os.listdir(dir_path):
            if not filename.endswith(F_WEBP): continue

            if filename.startswith('.'): continue  # 过滤隐藏文件

            if show_print:
                print(filename)

            webp_file_path = os.path.join(dir_path, filename)   # 原始文件地址
            

            self.webp_2_jpg(webp_file_path)

            if remove_origin:
                os.remove(webp_file_path)

    def resize_in_dir(self, dir_path: str = None, resize_width=2000):
        """
        压缩图片
        系统需要安装 imagemagick
        """

        for filename in os.listdir(dir_path):
            if not filename.endswith('.jpg'): continue

            if filename.startswith('.'): continue  # 过滤隐藏文件

            print(filename)

            file_path = os.path.join(dir_path, filename)    # 原始文件地址

            cmd = f'convert -quality 70 -resize {resize_width} {file_path} {file_path}'

            os.system(cmd)

    def trim_file(self, dir_path: str = None, target_format: str = None, trim_str: str = None):
        """去除文件名空格"""
        for filename in os.listdir(dir_path):
            if not filename.endswith(target_format): continue  # 过滤不符合的文件

            if filename.startswith('.'): continue  # 忽略隐藏文件

            # print(filename)
            if not trim_str:  # 没有设置去除的标志，默认空格
                new_filename = filename.strip()
            else:
                new_filename = filename.strip(trim_str)

            # 修改文件名
            shutil.move(
                os.path.join(dir_path, filename),
                os.path.join(dir_path, new_filename)
            )

    def img_2_ico(self, img_file_path: str = None):
        """图片转ico"""

        im = Image.open(img_file_path)

        # 调整大小
        im = im.resize((64, 64))

        file_name = img_file_path.rsplit('.', 1)[0]

        file_path = f'{file_name}.ico'

        # 保存为 ico格式
        im.save(file_path, format='ICO')

        return file_path

    def sort_file(self, dir_path: str = None):
        target_dir = f'{dir_path}-sorted'

        if not os.path.exists(target_dir):
            os.mkdir(target_dir)

        for index, file_name in enumerate(sorted(os.listdir(dir_path))):
            print(file_name)

            file_index = index + 1
            file_pos = file_name.rsplit('.', 1)[-1]

            origin_path = os.path.join(dir_path, file_name)
            new_path = os.path.join(target_dir, f'{file_index}.{file_pos}')

            print('     ', origin_path)
            print('     ', new_path)
            # shutil.move(origin_path, new_path)
            shutil.copy(origin_path, new_path)

        pass

    def down_big_img_and_crop(
            self,
            large_img_url=None,
            slice_width=None,
            slice_height=None,
            slice_num=3,
            download_dir='tmp',
            index_start=0,
            save_pos='JPEG'
    ):
        # 下载大图
        response = requests.get(large_img_url)
        img = Image.open(io.BytesIO(response.content))

        if not os.path.exists(download_dir):
            os.mkdir(download_dir)

        # 切片并保存
        for i in range(slice_num):  # 假设有 n 个切片
            # 计算每个切片的左上角坐标
            left = i * slice_width
            top = 0
            right = left + slice_width
            bottom = top + slice_height

            # 切片
            img_slice = img.crop((left, top, right, bottom))

            # 保存切片
            slice_filename = os.path.join(download_dir, f'{i + index_start + 1}.jpg')
            img_slice.save(slice_filename, save_pos)
            print(f'Downloaded: {slice_filename}')

        print('所有切片下载完成！')


if __name__ == '__main__':
    p = ''
    i = EasyImage()

    # webp 图片转 jpg
    # i.webp_2_jpg('a.webp')

    # 将文件夹下的所有 webp 转 jpg
    # i.webp_2_jpg_in_dir(dir_path=p,
    #                     remove_origin=True)

    # 将文件夹下的所有 文件，按照指定字符（默认空格）去除
    # i.trim_file(dir_path=p, target_format='.jpg', trim_str='0')

    # 压缩文件夹里面的jpg
    # i.resize_in_dir('/home/<USER>')

    # 图片转ico
    # i.img_2_ico('jay.jpeg')

    i.sort_file('/home/<USER>/Desktop/3')
