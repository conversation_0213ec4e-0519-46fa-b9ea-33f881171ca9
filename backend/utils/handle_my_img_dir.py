import os
import shutil

from utils.easy_file import EasyFile
from utils.easy_image import EasyImage

DIR_HANDLE_DOWN = '__HANDLE_DOWN'


def handle_dir_path(dir_path=None):
    """
    处理程序：
        1.扫描 dir_path 的所有文件夹和压缩包
        2.解压压缩包
        3.图片文件夹进行 webp转图片——>重新排序——>
    """

    handle_down_path = os.path.join(dir_path, DIR_HANDLE_DOWN)
    if not os.path.exists(handle_down_path):
        os.mkdir(handle_down_path)

    for file_name in os.listdir(dir_path):
        file_path = os.path.join(dir_path, file_name)

        if all([
            os.path.isdir(file_path) or file_name.endswith('.zip'),  # 是文件夹 或 压缩包
            not file_name.startswith('.'),  # 不是隐藏文件,
            file_name != DIR_HANDLE_DOWN,  # 不是目标文件夹
        ]):  # 满足条件，才处理
            print('\n————————————————————————————————')
            print('     ', file_name)

            # ———— 如果是压缩包，则解压 ————
            if file_name.endswith('.zip'):
                file_path = easy_file.decompress(file_path)
                print(f'解压后：{file_path}')

            # ———— 使用副本 ————
            copy_tag = '________copy'
            new_file_path = f'{file_path}{copy_tag}'  # 副本目录
            if os.path.exists(new_file_path):  # 复制之前，删除重复文件夹
                shutil.rmtree(new_file_path)
            shutil.copytree(file_path, new_file_path)  # 复制
            print(f'副本：{new_file_path}')
            if file_name.endswith('.zip'):  # 有副本后，如果是zip文件，则移除解压后的文件夹
                shutil.rmtree(file_path)

            # ———— 将文件夹的webp改图片 ————
            easy_img.webp_2_jpg_in_dir(new_file_path, remove_origin=True, show_print=False)

            # ———— 重排序 ————
            re_order_path = easy_file.re_order_dir_img(new_file_path, show_print=False)
            # 覆盖
            shutil.rmtree(new_file_path)
            shutil.copytree(re_order_path, new_file_path)
            shutil.rmtree(re_order_path)

            # ———— 移动到处理完毕文件夹 ————
            dir_name = os.path.basename(new_file_path).replace(copy_tag, '')
            target_path = os.path.join(handle_down_path, dir_name)
            if os.path.exists(target_path):
                shutil.rmtree(target_path)
            os.rename(
                new_file_path,
                target_path
            )


if __name__ == '__main__':
    easy_file = EasyFile()
    easy_img = EasyImage()

    handle_dir_path('/Users/<USER>/Desktop/tmp/handle_dir')
