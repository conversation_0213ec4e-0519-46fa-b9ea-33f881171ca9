import os
import shutil
import zipfile


class EasyFile:
    """
    文件操作封装
    """

    def decompress(self, file_path: str = None, rm_origin=False):
        """
        解压
        :param file_path:
        """

        # 获取 文件目录，文件名
        file_dir = os.path.dirname(file_path)
        file_name = os.path.basename(file_path)

        if '.zip' not in file_name:
            raise Exception('格式不支持')

        # 开始解压
        res_dir_path = ''  # 解压后目录位置
        try:
            zip_file = zipfile.ZipFile(file_path)
            for file_obj in zip_file.infolist():
                if any([file_obj.filename.startswith('__MACOSX'),
                        file_obj.filename.endswith('.DS_Store')]):  # 忽略
                    continue

                # 修改编码，覆盖对象属性
                file_obj.filename = file_obj.filename.encode('cp437').decode('utf-8')

                tmp = zip_file.extract(file_obj, file_dir)  # 解压

                if file_obj.is_dir():  # 是文件夹，则为 解压后目录位置
                    res_dir_path = tmp

            zip_file.close()
            # print(f'======解压成功！======')
        except zipfile.BadZipFile as e:
            print(e)
            print('非压缩文件')
        except Exception as e:

            print(e)
            pass

        if rm_origin:
            os.remove(file_path)

        return res_dir_path

    def compression(self, dir_path, out_zip_path=None, title=None):
        """
        压缩
        :param dir_path:    待压缩的文件夹
        :param out_zip_path: 输出的路径
        :param title: 文件名 xxx.zip
        :return:
        """

        # 获取 原始文件所在 文件夹路径
        origin_dir_path = os.path.dirname(dir_path)
        # 获取 文件名
        origin_dir_name = os.path.basename(dir_path)

        if not title:
            title = f'{origin_dir_name}.zip'

        if not out_zip_path:
            out_zip_path = origin_dir_path

        target_zip_full_path = os.path.join(out_zip_path, title)  # 最终的 zip路径

        zip_file = zipfile.ZipFile(target_zip_full_path, "w", zipfile.ZIP_DEFLATED)
        for path, li_dirs, li_files in os.walk(dir_path):
            # 去掉目标跟路径，只对目标文件夹下边的文件及文件夹进行压缩
            new_path = path.replace(dir_path, '')

            for filename in li_files:
                zip_file.write(os.path.join(path, filename), os.path.join(new_path, filename))
        zip_file.close()
        return target_zip_full_path

    def batch_compress(self, dir_path=None, delete_origin_dir=False):
        """
        批量压缩 某个路径下的所有文件夹
        """

        for tmp in os.listdir(dir_path):

            tmp_path = os.path.join(dir_path, tmp)

            # 判断是否为文件夹
            if not os.path.isdir(tmp_path):  # 忽略非 文件夹
                continue

            print(tmp)

            # 压缩文件夹
            self.compression(dir_path=tmp_path,
                             out_zip_path=dir_path,
                             title=f'{tmp}.zip')

            if delete_origin_dir:  # 删除压缩的原始文件夹
                shutil.rmtree(tmp_path)

    def re_order_dir_img(self,
                         img_dir_path,
                         show_print=True,
                         replace_origin=False):
        """
        读取指定文件夹的图片，重新排序后放在同层级 xxx_order 文件夹
        :param replace_origin:  是否 替换原始文件夹，名字不变
        :param show_print:
        :param img_dir_path:
        :return:
        """

        # 列出文件（过滤隐藏文件）
        li_img_name = [
            item
            for item in os.listdir(img_dir_path)
            if not item.startswith('.')
        ]
        if show_print:
            print(li_img_name)

        # 排序逻辑
        def _fn_order(x):
            """按照 数字大小排序，只适合纯数字 文件名"""
            file_name, sep = os.path.splitext(x)
            return int(file_name)

        # 排序
        try:
            li_img_name = sorted(li_img_name, key=_fn_order)
        except:
            if show_print:
                print('数字升序排序异常，改为基础升序排序...')
            li_img_name = sorted(li_img_name)

        if show_print:
            print('排序后：', li_img_name)

        # 新建副本
        new_dir_path = f'{img_dir_path}_order'
        # 创建新文件夹
        if not os.path.exists(new_dir_path):
            os.mkdir(new_dir_path)

        # 按新顺序复制到新文件夹
        for index, img_file in enumerate(li_img_name):
            _, pos = os.path.splitext(img_file)
            new_file_name = f'{index + 1}{pos}'
            shutil.copy(
                os.path.join(img_dir_path, img_file),
                os.path.join(new_dir_path, new_file_name)
            )

        # 是否替换原始文件夹
        if replace_origin:  # 替换：删掉原始，改名
            shutil.rmtree(img_dir_path)  # 删除

            shutil.move(
                new_dir_path,
                img_dir_path
            )
            return img_dir_path
        else:
            return new_dir_path


if __name__ == '__main__':
    easy_file = EasyFile()
    # r = easy_file.decompress('xx')
    # print(f'输出文件夹: {r}')

    # r = easy_file.compression('/Users/<USER>/Desktop/a/b')
    # print(r)

    # r = easy_file.compression('./tmp/mysql2')

    easy_file.re_order_dir_img('/Users/<USER>/Desktop/tmp/dd/data_fake', replace_origin=True)
    # r = easy_file.compression('./tmp/mysql2')
