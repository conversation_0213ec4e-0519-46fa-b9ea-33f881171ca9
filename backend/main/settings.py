"""
Django settings for main project.

Generated by 'django-admin startproject' using Django 4.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""
import os
from pathlib import Path

import environ

env = environ.Env()
environ.Env.read_env('.env')  # 读取 .env 文件

DEBUG = env.bool('DEBUG', default=False)

BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-fb0w_em(zo5(-!@q(uj7&4#cczj61nn$_%*%z)!qeyt1u+k@vd"

ALLOWED_HOSTS = [
    '*'
]

CSRF_TRUSTED_ORIGINS = [
    env('CSRF_TRUSTED_ORIGIN'),    # 生产
]

CORS_ALLOWED_ORIGINS = [
    env('CORS_ALLOWED_ORIGIN'),  # 前后端跨域，防止 403
]

# Application definition

INSTALLED_APPS = [
    'simpleui',
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    'media_station',  # 媒体
    'api',  # api
]

# admin后台 自定义侧边栏菜单
SIMPLEUI_CONFIG = {
    'system_keep': False,  # 是否使用系统默认菜单，自定义时候关闭
    # 自定义 排序与过滤，不填写则会 按默认排序 加载全部数据。 空列表[] 则全部不显示
    'menu_display': ['媒体库', '认证和授权'],
    # 是否开启动态菜单
    'dynamic': False,
    # 自定义菜单
    'menus': [
        {
            'name': '媒体库',
            'models': [
                {'name': '图集', 'url': 'media_station/books/', 'icon': 'fa fa-book'},
                {'name': '图片', 'url': 'media_station/bookpages/'},
                {'name': '类型', 'url': 'media_station/genres/'},
                {'name': '标签', 'url': 'media_station/tags/'},
                {'name': '专题', 'url': 'media_station/subject/'},
                {'name': '专题-图册-管理', 'url': 'media_station/subjectbooklink/'},
                {'name': '上传', 'url': '/upload/'},
                {'name': '图册列表', 'url': '/index/'},
            ]
        },
        {
            'name': '认证和授权',
            'icon': 'fa fa-shield-alt',
            'models': [
                {'name': '用户', 'url': 'auth/user/', 'icon': 'fa fa-user'},
                {'name': '角色', 'url': 'auth/group/', 'icon': 'fa fa-users-cog'},
            ]
        }
    ]
}

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",

    # 自定义中间件
    'middleware.exception_middleware.ExceptionMiddleware',
]

ROOT_URLCONF = "main.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / 'templates'],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "main.wsgi.application"

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        # "NAME": BASE_DIR / "db.sqlite3",
        "NAME": env('DB_SQLITE_PATH'),
    }
}

# 缓存 redis
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{env('REDIS_HOST')}:{env('REDIS_PORT')}/",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "PASSWORD": env('REDIS_PWD')
        }
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {"NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator", },
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator", },
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator", },
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator", },]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "zh-hans"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

# 静态 url 前缀
STATIC_URL = "static/"

# collectstatic 收集静态资源，供部署使用的 目录位置
STATIC_ROOT = os.path.join(BASE_DIR, '_static')

# 其它的公共静态文件
STATICFILES_DIRS = (
    os.path.join(BASE_DIR, 'static'),
)  # html load_static 能够生效

# 设置文件上传路径，图片上传、文件上传都会存放在此目录里
MEDIA_URL = 'media/'
# MEDIA_ROOT = os.path.join(BASE_DIR, MEDIA_URL)
MEDIA_ROOT = env('MEDIA_ROOT_PATH')

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"
