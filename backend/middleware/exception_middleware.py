import logging
import traceback

from django.middleware.common import MiddlewareMixin


class ExceptionMiddleware(MiddlewareMixin):
    """统一异常处理中间件"""

    def process_exception(self, request, exception):
        """
        统一异常处理
        :param request: 请求对象
        :param exception: 异常对象
        :return:
        """

        # 异常处理

        traceback.print_exc()

        print(exception)

        logging.exception(exception)
        logging.exception(traceback.format_exc())   # 堆栈信息

        return None
