

import json
from media_station.models import Subject, SubjectBookLink
from django.http import JsonResponse

from utils import out


def api_subjects_list(request):
    """专题-列表"""

    params = request.GET

    key = params.get('key', '')
    page = int(params.get('page', 1))
    size = int(params.get('size', 10))

    res = Subject.objects

    if key:
        res = res.filter(title__icontains=key)

    total = res.count()  # 数量

    start = (page - 1) * size

    li_out = [
        item.subject_info()
        for item in res.order_by('asc_priority', '-created_at')[start: start + size]
    ]

    return JsonResponse(
        out.send(
            msg='专题列表',
            data={
                'list': li_out,
                'count': total,
                'size': size,
            }
        )
    )


def api_subject_add(request):
    """专题-添加"""

    jo = json.loads(request.body)
    title = jo.get("title", "")

    # 判重复
    if Subject.objects.filter(title=title).first():
        return JsonResponse(out.ADD_ERROR_OF_REPEAT)

    # 添加
    Subject.objects.create(title=title)
    return JsonResponse(out.ADD_SUCCESS)


def api_subjects_sort(request):
    """专题-排序"""

    jo = json.loads(request.body)
    li_subjects_id = jo.get("li_subjects_id", "")

    # 循环更新 asc_priority
    for index, subject_id in enumerate(li_subjects_id):
        Subject.objects.filter(
            id=subject_id
        ).update(
            asc_priority=index + 1  # 按照 index顺序更新
        )

    return JsonResponse(
        out.send(msg="排序成功")
    )


def api_subject_details(request, subject_id):
    """专题-详情"""

    db_subject = Subject.objects.filter(id=subject_id).first()

    return JsonResponse(
        out.send(
            msg='专题-详情',
            data={'subject': db_subject.subject_details()}
        )
    )


def api_subject_books_sort(request, subject_id):
    """专题-详情-书本-排序"""
    jo = json.loads(request.body)
    li_book_id = jo.get("li_book_id", "")

    # 循环更新 asc_priority
    for index, book_id in enumerate(li_book_id):
        SubjectBookLink.objects.filter(
            subject_id=subject_id,
            book_id=book_id
        ).update(
            asc_priority=index + 1  # 按照 index顺序更新
        )

    # 更新缓存
    SubjectBookLink.fresh_cache(subject_id)

    return JsonResponse(
        out.send(msg='更新成功')
    )


def api_subject_book_del(request, subject_id, book_id):
    """专题-书本-删除"""

    SubjectBookLink.objects.filter(
        subject_id=subject_id,
        book_id=book_id
    ).delete()

    # 更新缓存
    SubjectBookLink.fresh_cache(subject_id)

    return JsonResponse(out.DEL_SUCCESS)
