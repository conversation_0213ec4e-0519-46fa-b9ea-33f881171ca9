"""
URL configuration for dj_my_steward project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path

from . import views, views_subject

urlpatterns = [
    path('genres/', views.api_genres_list),  # 类型-列表
    path('tags/', views.api_tags_list),  # 标签-列表
    path('subjects/', views.api_subjects_list),  # 专题-列表
    path('tag/add/', views.api_tag_add),  # 标签-添加
    path('books/', views.api_books_list),  # 书本-列表
    path('books/combine/', views.api_books_combine),  # 书本-合并
    path('books/upload/', views.api_books_upload),  # 书本-上传
    path('book/<book_id>/', views.api_book_details),  # 获取-书本-详情
    path('book/<book_id>/pages/list', views.api_book_pages_list),  # 获取-书本-页面-列表
    path('book/<book_id>/download/', views.api_book_download),  # 书本-下载
    path('book/<book_id>/del/', views.api_book_del),  # 书本-删除
    path('book/<book_id>/<page_id>/del/', views.api_book_page_del),  # 书本-页面-删除
    path('book/<book_id>/pages/sort/', views.api_book_pages_sort),   # 书本-页面-排序
    path('book/<book_id>/title/', views.api_books_title_update),  # 修改-书本-标题
    path('book/<book_id>/collect/', views.api_books_collect),  # 书本-切换收藏
    path('book/<book_id>/genre/', views.api_books_genre),  # 书本-修改-类型
    path('book/<book_id>/tags/', views.api_books_tags_update),  # 修改-书本-标签
    path('book/<book_id>/subjects/', views.api_books_subjects_update),  # 修改-书本-专题

    path('subjects/', views_subject.api_subjects_list),  # 专题-列表
    path('subjects/sort/', views_subject.api_subjects_sort),  # 专题-排序
    path('subject/add/', views_subject.api_subject_add),  # 专题-添加
    path('subject/<subject_id>/', views_subject.api_subject_details),  # 专题-详情
    path('subject/<subject_id>/books/sort/',
         views_subject.api_subject_books_sort),   # 专题-详情-书本-排序
    path('subject/<subject_id>/<book_id>/del/',
         views_subject.api_subject_book_del),  # 删除-专题-书本
]
