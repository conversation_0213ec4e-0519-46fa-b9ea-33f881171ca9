# gunicorn_config.py

# 进程配置
daemon = False  # 守护进程运行
bind = '0.0.0.0:12345'  # 监听的地址和端口
workers = 2  # 工作进程数量
worker_class = 'sync'  # 工作类
timeout = 30  # 超时设置

# 日志配置
loglevel = 'info'  # 日志级别
accesslog = '/app/logs/access.log'  # 访问日志文件
errorlog = '/app/logs/error.log'    # 错误日志文件
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 可选: 指定 Django 的 WSGI 应用

# wsgi_app = 'main.wsgi.application'  # 替换为您的项目名称