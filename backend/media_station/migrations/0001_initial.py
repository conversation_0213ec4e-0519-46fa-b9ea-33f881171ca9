# Generated by Django 4.0.3 on 2023-08-01 16:15

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Image',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='标题')),
                ('page_count', models.IntegerField(default=0, verbose_name='页数')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='修改时间')),
            ],
        ),
        migrations.CreateModel(
            name='ImagePage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('img_path', models.CharField(max_length=255, verbose_name='地址')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='修改时间')),
                ('image', models.ForeignKey(max_length=255, on_delete=django.db.models.deletion.DO_NOTHING, to='media_station.image', verbose_name='图册')),
            ],
        ),
    ]
