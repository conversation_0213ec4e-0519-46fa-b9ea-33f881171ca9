# Generated by Django 4.0.3 on 2023-12-25 09:30

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('media_station', '0013_tags_books_tags'),
    ]

    operations = [
        migrations.CreateModel(
            name='BookFavors',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='media_station.books', verbose_name='图册')),
            ],
            options={
                'verbose_name': '喜欢',
                'verbose_name_plural': '喜欢',
                'db_table': 'book_favors',
            },
        ),
    ]
