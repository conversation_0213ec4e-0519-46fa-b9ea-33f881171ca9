# Generated by Django 4.0.3 on 2023-12-22 07:18

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('media_station', '0011_genres_books_genre'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='genres',
            options={'verbose_name': '类型', 'verbose_name_plural': '类型'},
        ),
        migrations.AddField(
            model_name='genres',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间'),
        ),
        migrations.AddField(
            model_name='genres',
            name='updated_at',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='修改时间'),
        ),
        migrations.AlterModelTable(
            name='genres',
            table='genres',
        ),
    ]
