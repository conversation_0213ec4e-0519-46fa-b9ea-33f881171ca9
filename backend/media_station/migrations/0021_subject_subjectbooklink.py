# Generated by Django 4.0.3 on 2024-04-23 08:16

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('media_station', '0020_alter_books_tags'),
    ]

    operations = [
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='专题')),
                ('thumb_img_path', models.ImageField(max_length=255, upload_to='subjects', verbose_name='封面')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='修改时间')),
            ],
        ),
        migrations.CreateModel(
            name='SubjectBookLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('asc_priority', models.FloatField(default=999, verbose_name='排序权重')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='media_station.books')),
                ('subject', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='media_station.subject')),
            ],
        ),
    ]
