import os
import re
import shutil

from django.contrib import admin, messages
# Register your models here.
from django.http import FileResponse
from django.utils.safestring import mark_safe

from main.settings import MEDIA_ROOT, MEDIA_URL
from media_station.constants import RESOURCE_SUB_DIR
from media_station.models import BookCollections, Books, BookPages, Genres, Tags, Subject, SubjectBookLink


def show_img(src_link: str = None):
    return mark_safe(f'<img src="{src_link}" style="max-height:40px;max-width:40px;margin:0 auto">')


@admin.register(Books)
class BookAdmin(admin.ModelAdmin):
    """图集"""

    # 搜索关键词
    search_fields = ('title',)
    # 列表里显示想要显示的字段
    list_display = ('id', 'title', 'thumb', 'is_visible', 'only_subject', 'genre', 'show_tags',
                    'page_count', 'order', 'created_at', 'updated_at')
    list_filter = ('genre', 'tags', 'is_visible', 'only_subject', 'remarks')  # 筛选字段
    list_per_page = 50  # 满10条数据就自动分页
    ordering = ('-id',)  # 后台数据列表排序方式
    list_display_links = ('id', 'thumb')  # 设置哪些字段可以点击进入编辑界面
    list_editable = ('title', 'is_visible', 'only_subject',
                     'order', 'genre',)  # 列表页-直接编辑功能
    actions = ('download_book', 'merge_books', 'only_subject_view')  # 自定义操作

    @admin.display(description='封面')
    def thumb(self, obj):
        return show_img(obj.get_book_first_img())

    def delete_model(self, request, obj):
        """重写 详情-删除"""
        # 获取 文件夹
        title = obj.get_book_save_dir_name()
        if title:
            Books.delete_dir_with_title(title)  # 删除 本地文件夹

        super().delete_model(request, obj)

    def delete_queryset(self, request, queryset):
        """重写 列表-批量删除"""

        # 遍历 book对象
        for book_item in queryset:
            # 通过 book_page 获取 文件夹名字（title 支持改名，可能对不上文件夹名）
            title = book_item.get_book_save_dir_name()
            if title:
                Books.delete_dir_with_title(title)  # 删除文件夹

        super().delete_queryset(request, queryset)

    def save_model(self, request, obj, form, change):
        """
        保存 触发
        :param request:
        :param obj:
        :param form:
        :param change:
        :return:
        """

        obj.page_count = obj.bookpages_set.count()  # 重置页数

        # 刷新cache缓存
        Books.fresh_cache(obj.id)

        # 提交修改
        obj.save()

    @admin.display(description='下载图册')
    def download_book(self, request, queryset):
        """
        下载图册 TODO: 调整临时文件夹
        """

        if len(queryset) != 1:
            return

        book_item = queryset[0]

        # 执行下载操作，打包 zip
        zip_out_path, out_zip_name = book_item.download_book()

        # 作为附件 返回
        f = open(zip_out_path, 'rb')
        resp = FileResponse(f, as_attachment=True, filename=out_zip_name)

        # 移除 zip 文件
        try:
            os.remove(zip_out_path)
        except:  # window系统可能无法移除
            pass

        return resp

    @admin.display(description='仅专题可见')
    def only_subject_view(self, request, queryset):
        """仅专题可见"""

        queryset.update(
            only_subject=True
        )
        messages.success(request, '修改成功')

    @admin.display(description='合并')
    def merge_books(self, request, queryset):
        """
        合并
            1.创建新的本地文件夹
            2.循环将图册的图片，按顺序复制到新文件夹
            3.标题为 图册1-图册2
            4.备注为 合并来源：图册1、图册2
            5.继承 所有图册的 类型、标签、专题、已收藏
        """

        if len(queryset) < 2:
            messages.error(request, '请至少选择2个图册')
            return

        # 遍历所有图册，获取标题，统计总图片数量
        titles = []
        total_page = 0
        all_tags = set()  # 收集标签
        all_subjects = set()  # 收集专题
        default_genre = None  # 第一个非空类型
        is_collect = False  # 是否已收藏

        for book_item in queryset:
            titles.append(book_item.title)
            total_page += book_item.get_book_page_count()

            # 收集标签
            all_tags.update(book_item.tags.all())

            # 收集专题
            for subject_link in book_item.subjectbooklink_set.all():
                all_subjects.add(subject_link.subject)

            # 记录第一个非空的类型作为默认类型
            if not default_genre and book_item.genre:
                default_genre = book_item.genre

            # 记录是否已收藏（只要有任意一个book已收藏）
            if book_item.get_is_collect():
                is_collect = True

        # 新的文件名（也是文件夹名）
        new_title = 'combine-' + ';'.join(titles)
        new_title = re.sub(r'[^a-zA-Z0-9\u4e00-\u9fa5\-\_]', '_', new_title)  # 将标题中的非法字符(不是 字母/数字/中文)替换为下划线

        # 保存路径
        target_save_path = os.path.join(
            MEDIA_ROOT,
            RESOURCE_SUB_DIR,
            new_title
        )

        # 创建
        if not os.path.exists(target_save_path):
            os.makedirs(target_save_path)

        # 创建 db book
        db_new_book = Books(
            title=new_title,
            remarks=f'合并来源：{"; ".join(titles)}',
            is_visible=True,
            genre=default_genre,  # 设置类型(有可能为空)
        )
        db_new_book.save()

        # 添加所有标签
        if all_tags:
            db_new_book.tags.set(all_tags)

        # 添加所有专题关联
        for subject in all_subjects:
            SubjectBookLink.objects.create(
                subject=subject,
                book=db_new_book
            )

        # 设置是否已收藏
        if is_collect:
            BookCollections.objects.create(
                book=db_new_book
            )

        # 循环复制 每本书-每一页到 新文件夹
        index_img = 0  # 图片索引
        for book_item in queryset:  # 遍历每本书
            for page_item in book_item.bookpages_set.order_by('-order', 'page').all():  # 遍历 书本的每一页
                img_name = f'{index_img + 1}.jpg'  # 图片名称

                # 最终图片路径
                final_img_path = os.path.join(target_save_path, img_name)

                # 复制图片
                shutil.copy(page_item.img_path.path, final_img_path)

                # 保存每一页到db
                db_book_page = BookPages(
                    image=db_new_book,
                    img_path=f'{RESOURCE_SUB_DIR}/{new_title}/{img_name}',
                    page=index_img + 1,  # 页码
                )
                db_book_page.save()

                index_img += 1  # 索引+1

        # 更新页数
        db_new_book.page_count = index_img
        db_new_book.save()

        messages.success(request, '合并成功')

    @admin.display(description='标签')
    def show_tags(self, objs):
        """标签显示"""
        return [
            item.value
            for item in objs.get_tags()
        ]


@admin.register(BookPages)
class BookPagesAdmin(admin.ModelAdmin):
    """图片"""

    # 搜索关键词
    # search_fields = ('title',)
    # 列表里显示想要显示的字段
    list_display = ('id', 'img', 'page', 'image', 'img_path',
                    'order', 'created_at', 'updated_at')
    list_filter = ('image',)  # 筛选字段
    list_per_page = 20  # 满10条数据就自动分页
    ordering = ('-image__created_at', 'image__title', '-order')  # 后台数据列表排序方式
    list_editable = ('order',)  # 列表页-直接编辑功能

    # list_display_links = ('title',)  # 设置哪些字段可以点击进入编辑界面

    @admin.display(description='图片')
    def img(self, obj):
        return show_img(f'/{MEDIA_URL}{obj.img_path}')

    def save_model(self, request, obj, form, change):
        """
        添加 触发
        :param request:
        :param obj:
        :param form:
        :param change:
        :return:
        """

        print(obj)

        # 提交修改
        super().save_model(request, obj, form, change)
        pass

    def delete_queryset(self, request, queryset):
        """重写 列表-批量删除"""

        # 遍历对象
        for b_page in queryset:
            # 删除 本地图片
            os.remove(b_page.img_path.path)

        super().delete_queryset(request, queryset)


@admin.register(Genres)
class GenresAdmin(admin.ModelAdmin):
    list_display = ('id', 'value', 'show_book_count',
                    'created_at', 'updated_at')
    list_per_page = 20

    @admin.display(description='绑定数量')
    def show_book_count(self, obj: Genres):
        return obj.get_book_count()


@admin.register(Tags)
class TagsAdmin(admin.ModelAdmin):
    list_display = ('id', 'value', 'show_book_count',
                    'created_at', 'updated_at')
    list_per_page = 20

    @admin.display(description='绑定数量')
    def show_book_count(self, obj: Tags):
        return obj.get_book_count()


@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    search_fields = ('title',)
    list_display = ('id', 'thumb', 'title', 'counting',
                    'asc_priority', 'created_at', 'updated_at')
    list_per_page = 20
    list_editable = ('title', 'asc_priority',)
    list_display_links = ('id', 'thumb',)

    @admin.display(description='封面')
    def thumb(self, obj):
        if obj.thumb_img_path:
            return show_img(f'/{MEDIA_URL}{obj.thumb_img_path}')

        return '——'

    @admin.display(description='数量')
    def counting(self, obj):
        return obj.get_book_count()


@admin.register(SubjectBookLink)
class SubjectBookLinkAdmin(admin.ModelAdmin):
    list_filter = ('subject', 'book')
    list_display = ('id', 'subject', 'book_thumb',
                    'book', 'asc_priority', 'created_at')
    list_per_page = 50
    list_editable = ('asc_priority',)
    ordering = ('asc_priority',)
    actions = ('scan_asc_priority',)  # 自定义操作

    @admin.display(description='图册-封面')
    def book_thumb(self, obj):
        return show_img(
            obj.book.get_book_first_img()
        )

    @admin.display(description='识别排序权重')
    def scan_asc_priority(self, request, queryset):
        for link_item in queryset:
            subject_title = link_item.subject.title
            book_title = link_item.book.title

            try:
                tmp = str(book_title).strip() \
                    .replace(subject_title, '') \
                    .replace('第', '') \
                    .replace('话', '').strip()

                # 空格切分 取第一个
                tmp = tmp.split()[0]

                if not tmp.isnumeric():
                    # - 切分 取第一个
                    tmp = tmp.split('-')[0]

                asc_priority = int(tmp)

                # 修改 link 权重
                link_item.asc_priority = asc_priority
                # 保存
                link_item.save()
            except:
                pass

        messages.success(request, '识别排序权重成功')
