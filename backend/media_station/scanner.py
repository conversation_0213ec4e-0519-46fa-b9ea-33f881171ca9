import os
import shutil

from main.settings import MEDIA_ROOT
from utils.easy_file import EasyFile

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "main.settings")  # manage.py文件中有同样的环境配置
import django

django.setup()

from media_station.constants import RESOURCE_SUB_DIR
from media_station.models import Books, BookPages
from utils.easy_image import EasyImage


def handle():
    resource_path = './Scanner'

    easy_file = EasyFile()

    for title in os.listdir(resource_path):
        now_path = os.path.join(resource_path, title)

        # 遇到 zip文件，则解压缩再解析
        if title.endswith('.zip'):
            print(f'解压: {title}')
            de_dir_path = easy_file.decompress(now_path, rm_origin=True)  # 解压

            title = os.path.basename(de_dir_path)  # 解压文件夹名
            now_path = de_dir_path  # 覆盖解压文件夹路径
        else:  # 非 zip文件
            # 过滤非文件夹文件
            if not os.path.isdir(now_path):
                continue

        print(title)

        # 判断重复
        if Books.objects.filter(title=title).first():
            print('     ~~ 已存在，忽略 ~~')
            continue

        # 保存路径
        target_save_path = os.path.join(
            MEDIA_ROOT,
            RESOURCE_SUB_DIR,
            title
        )

        # 移动到资源文件夹
        shutil.move(now_path, target_save_path)

        # 排序
        easy_file.re_order_dir_img(
            target_save_path,
            show_print=False,
            replace_origin=True
        )

        # ———— 保存 整本书到db ————
        db_image = Books(title=title, is_visible=True)
        db_image.save()

        # 遍历文件夹，保存每一页到db
        total_page = 0

        easy_img = EasyImage()

        # ———— 将文件夹下的所有 文件，按照指定字符（默认空格）去除 ————
        easy_img.trim_file(dir_path=target_save_path, target_format='.jpg', trim_str='0')
        easy_img.trim_file(dir_path=target_save_path, target_format='.png', trim_str='0')

        # 遍历 资源文件夹
        for file_name in sorted(os.listdir(target_save_path)):
            if file_name.startswith('.'):  # 忽略隐藏文件
                continue

            # ———— webp格式，直接转换为 jpg格式 ————
            if file_name.endswith('.webp'):
                # webp 转 jpg
                res = easy_img.webp_2_jpg(os.path.join(target_save_path, file_name), remove_origin=True)
                file_name = os.path.basename(res)  # 重置 filename，后续可以上传

            if any([
                file_name.endswith('.jpg'),
                file_name.endswith('.jpeg'),
                file_name.endswith('.png')
            ]):
                # 保存到每一页到db

                add_jo = {
                    'image': db_image,
                    'img_path': f'{RESOURCE_SUB_DIR}/{title}/{file_name}'  # 保存格式：image_books/标题/图片文件名
                }
                page_str = file_name.rsplit('.', 1)[0]
                if page_str.isdigit():  # 标题就是数字，则直接认为是页码
                    add_jo['page'] = page_str

                db_image_page = BookPages(
                    **add_jo
                )
                db_image_page.save()
                total_page += 1

        # 设置 页数
        db_image.page_count = total_page
        db_image.save()
        print(f'     成功，扫描 {total_page} 页')
    pass


if __name__ == '__main__':
    # 1. webp——>jp
    # 2. 01.jpg ——> 1.jpg
    # 3. 从1.jpg开始重排序
    handle()
