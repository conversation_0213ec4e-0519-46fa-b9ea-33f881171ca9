import datetime
import os
import shutil

from django.conf import settings as global_settings
from django.core.cache import cache
from django.db import models
from django.utils import timezone
from django.utils.safestring import mark_safe
from main.settings import MEDIA_ROOT
from media_station.constants import RESOURCE_SUB_DIR
from utils.easy_file import EasyFile


class Genres(models.Model):
    value = models.CharField('类型', max_length=255)
    created_at = models.DateTimeField(
        verbose_name='创建时间', default=timezone.now)
    updated_at = models.DateTimeField(
        verbose_name='修改时间', default=timezone.now)

    class Meta:
        verbose_name = '类型'
        verbose_name_plural = verbose_name
        db_table = 'genres'

    def __str__(self):
        return self.value

    def get_book_count(self):
        return self.books_set.filter(is_visible=True).count()


class Tags(models.Model):
    value = models.CharField('标签', max_length=255)
    created_at = models.DateTimeField(
        verbose_name='创建时间', default=timezone.now)
    updated_at = models.DateTimeField(
        verbose_name='修改时间', default=timezone.now)

    class Meta:
        verbose_name = '标签'
        verbose_name_plural = verbose_name
        db_table = 'tags'

    def __str__(self):
        return self.value

    def get_book_count(self):
        """book数量"""
        return self.books_set.filter(is_visible=True).count()

    def get_tag_info(self):
        return {
            'id': self.id,
            'value': self.value,
            'count': self.get_book_count()
        }


class Books(models.Model):
    title = models.CharField('标题', max_length=255)

    genre = models.ForeignKey(
        Genres,
        verbose_name='类型',
        on_delete=models.DO_NOTHING,
        default=None,
        null=True,
        blank=True)
    tags = models.ManyToManyField(
        Tags, verbose_name='标签', blank=True)

    page_count = models.IntegerField('页数', default=0)
    order = models.FloatField('排序', default=0)
    is_visible = models.BooleanField('可见', default=False)
    only_subject = models.BooleanField('仅专题可见', default=False)

    views = models.IntegerField('浏览量', default=0)
    created_at = models.DateTimeField(
        verbose_name='创建时间', default=timezone.now)
    updated_at = models.DateTimeField(
        verbose_name='修改时间', default=timezone.now)
    remarks = models.TextField('备注', default='')
    author = models.CharField('作者', null=True, blank=True, max_length=255, default='')

    class Meta:
        verbose_name = '图集'
        verbose_name_plural = verbose_name
        db_table = 'books'

    def __str__(self):
        return self.title

    @classmethod
    def delete_dir_with_title(cls, title):
        # 删除 media 的本地文件
        delete_dir_path = os.path.join(MEDIA_ROOT, RESOURCE_SUB_DIR, title)
        if os.path.exists(delete_dir_path):  # 删除本地文件夹
            shutil.rmtree(delete_dir_path)

    def download_book(self):
        """下载书本"""

        # 实际书名
        title = self.title

        # 项目根目录
        project_root = global_settings.BASE_DIR

        # 准备下载zip 的临时文件夹
        download_dir = os.path.join(project_root, 'tmp', 'download')

        # 下载对应文件夹
        download_path = os.path.join(download_dir, title)

        # 待下载的文件夹
        if not os.path.exists(download_path):
            os.makedirs(download_path)

        # 按照 -order, page 排序，重新改名，生成文件夹
        li_page = BookPages.objects.filter(
            image_id=self.id,  # 筛选 book 的 page
        ).order_by('-order', 'page').all()

        for index, page_item in enumerate(li_page):
            old_page_path = page_item.img_path.path  # 实际存储的图片地址

            # 新的下载图片路径
            new_img_name = f'{index + 1}.jpg'
            new_page_path = os.path.join(download_path, new_img_name)

            # 复制到新的待下载文件夹
            shutil.copyfile(old_page_path, new_page_path)

        # 压缩 download 文件夹
        out_zip_name = f'{title}.zip'
        zip_out_path = EasyFile().compression(dir_path=download_path,
                                              out_zip_path=download_dir,
                                              title=out_zip_name)

        # 移除 download 文件夹
        shutil.rmtree(download_path)

        return zip_out_path, out_zip_name

    def get_book_save_dir_name(self):
        """
        获取 保存的文件夹名字
        """
        a_book_page = self.bookpages_set.first()

        sep = os.sep  # win:\\,  mac: /

        try:
            return a_book_page.img_path.path.replace(
                MEDIA_ROOT, ''
            ).strip(sep).replace(
                RESOURCE_SUB_DIR, ''
            ).strip(sep).split(sep)[0]
        except BaseException:
            return ''

    def get_book_first_img(self):
        """获取 封面"""
        first_img = self.bookpages_set.order_by('-order', 'page').first()
        if not first_img:
            return ''

        return first_img.img_path.url

    def get_tags(self):
        """获取所有 标签对象数组"""
        return self.tags.all()

    def get_is_collect(self):
        return not not self.bookcollections_set.first()

    @classmethod
    def gen_cache_key(cls, book_id):
        return f'key__aj_book/{book_id}/'

    @classmethod
    def fresh_cache(cls, book_id):
        """刷新cache缓存"""
        cache_key = cls.gen_cache_key(book_id)

        cache.delete(cache_key)

    def get_book_page_count(self):
        """获取图册总页数，通过db查询page"""
        return self.bookpages_set.count()

    def book_info(self):
        """图册-基本信息"""

        # 判断是否有缓存，无则获取并保存
        cache_key = self.gen_cache_key(self.id)

        res = cache.get(cache_key)
        if not res:
            res = {
                'id': self.id,
                'title': self.title,
                'page_count': self.page_count,
                'order': self.order,
                'genre': self.genre.value if self.genre else '',
                'tags': [item.get_tag_info() for item in self.get_tags()],
                'first_img': self.get_book_first_img(),
                'views': self.views,
                'created_at': datetime.datetime.strftime(self.created_at, '%Y-%m-%d %H:%M:%S'),
                'updated_at': datetime.datetime.strftime(self.updated_at, '%Y-%m-%d %H:%M:%S'),
                'is_collect': self.get_is_collect(),
                'li_subjects': [
                    {
                        'id': link.subject.id,
                        'title': link.subject.title,
                        'thumb_url': link.subject.get_thumb_url(),
                    }
                    for link in self.subjectbooklink_set.all()
                ],
                'remarks': self.remarks,    # 备注
                'author': self.author,   # 作者
            }

            # 保存到 cache
            cache.set(cache_key, res, timeout=60 * 60)

        return res

    def book_details(self):
        """书本-详情"""

        # 找出所有专题
        li_subject_book = []
        li_subject_of_book = (
            SubjectBookLink.objects.filter(
                book_id=self.id  # 筛选 当前 图册，找出所有绑定了的专题
            )
            .order_by("subject__asc_priority")
            .all()
        )
        for subject_link in li_subject_of_book:  # 遍历所有专题
            # 获取 当前专题的所有 book
            subject_id = subject_link.subject_id

            li_same_subject_book_link_list = (
                Books.objects.filter(
                    is_visible=True, subjectbooklink__subject_id=subject_id  # 筛选 专题id
                )
                .order_by("subjectbooklink__asc_priority", "title")
                .all()
            )

            li_subject_book.append(
                {
                    "id": subject_link.subject.id,
                    "title": subject_link.subject.title,
                    "li_books": [
                        {
                            "id": book.id,
                            "title": book.title,
                            # 当前书本在此专题中的排序
                            "asc_priority": SubjectBookLink.objects.filter(
                                subject_id=subject_id, book_id=book.id
                            ).values("asc_priority").first()["asc_priority"],
                            "first_img": book.get_book_first_img(),
                        }
                        for book in li_same_subject_book_link_list
                    ],
                }
            )

        return {
            **self.book_info(),
            'li_subject_book': li_subject_book,
        }


class BookPages(models.Model):
    image = models.ForeignKey(Books, verbose_name='图册',
                              max_length=255, on_delete=models.CASCADE)

    # 直接上传=.../image_books/1.jpg，zip上传=xxx/image_books/<title>/1.jpg
    img_path = models.ImageField('地址', max_length=255, upload_to='image_books')

    page = models.IntegerField('页码', default=999)
    # 重排顺序，越大越靠前。优先级 -order > page
    order = models.IntegerField('倒序排序', default=0)
    created_at = models.DateTimeField(
        verbose_name='创建时间', default=timezone.now)
    updated_at = models.DateTimeField(
        verbose_name='修改时间', default=timezone.now)

    class Meta:
        verbose_name = verbose_name_plural = '图片页'
        db_table = 'book_pages'

    def show_img(self):
        """图片显示"""

        img_len = 60

        if self.img_path:  # 使用封面图片
            return mark_safe(
                f'<img src="{self.img_path.url}" style="height:{img_len}px;width:{img_len}px;object-fit:contain" />')

        return '——'

    def book_page_base_info(self):
        return {
            'id': self.id,
            'img_path': self.img_path.url,
            'order': self.order,
            'page': self.page,
        }


class BookCollections(models.Model):
    book = models.ForeignKey(Books, verbose_name='图册',
                             on_delete=models.CASCADE)
    created_at = models.DateTimeField(
        verbose_name='创建时间', default=timezone.now)

    class Meta:
        verbose_name = verbose_name_plural = '收藏'
        db_table = 'book_collections'


class Subject(models.Model):
    title = models.CharField('专题', max_length=255)

    thumb_img_path = models.ImageField(
        '封面', max_length=255, upload_to='subjects', null=True, blank=True)
    asc_priority = models.FloatField('正序权重', default=999)  # 从小到大排列
    created_at = models.DateTimeField(
        verbose_name='创建时间', default=timezone.now)
    updated_at = models.DateTimeField(
        verbose_name='修改时间', default=timezone.now)

    class Meta:
        verbose_name = verbose_name_plural = '专题'
        db_table = 'subjects'

    def __str__(self):
        return self.title

    def get_book_count(self):
        return self.subjectbooklink_set.count()

    def get_thumb_url(self):
        if self.thumb_img_path:
            return self.thumb_img_path.url

        first_link = self.subjectbooklink_set.order_by('asc_priority').first()
        if first_link:
            return first_link.book.get_book_first_img()

        return ''

    def _get_subject_books_list(self, subject_id, no_cache: int = 0):
        """获取-专题-所有书籍"""
        c_key = SubjectBookLink.gen_cache_key(subject_id)

        # 查看缓存
        li_out_cached = cache.get(c_key)

        if no_cache or not li_out_cached:  # 不存在，查询
            res = Books.objects.filter(
                is_visible=True,
                subjectbooklink__subject_id=subject_id  # 筛选 专题id
            ).order_by(
                'subjectbooklink__asc_priority',
                'title'
            ).all()

            li_out_cached = [
                {
                    **item.book_info(),
                    # 专题中的 图册权重
                    'asc_priority':
                        SubjectBookLink.objects.filter(
                            subject_id=subject_id, book_id=item.id
                    ).values('asc_priority').first()['asc_priority']
                }
                for item in res
            ]

            # 保存到缓存
            cache.set(c_key, li_out_cached)

        return li_out_cached

    def subject_info(self):
        return {
            'id': self.id,
            'title': self.title,
            'book_count': self.get_book_count(),
            'thumb_url': self.get_thumb_url(),
            'asc_priority': self.asc_priority,
            'created_at': datetime.datetime.strftime(
                self.created_at,
                '%Y-%m-%d %H:%M:%S'),
            'updated_at': datetime.datetime.strftime(
                self.updated_at,
                '%Y-%m-%d %H:%M:%S')}

    def subject_details(self, no_cache: int = 0):
        return {
            **self.subject_info(),
            # 关联的数据
            'li_books': self._get_subject_books_list(self.id, no_cache)
        }


class SubjectBookLink(models.Model):
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE)
    book = models.ForeignKey(Books, on_delete=models.CASCADE)
    asc_priority = models.FloatField('正序权重', default=999)  # 从小到大排列
    created_at = models.DateTimeField(
        verbose_name='创建时间', default=timezone.now)

    class Meta:
        verbose_name = verbose_name_plural = '专题-图册-管理'
        db_table = 'subjects_books_link'

    @classmethod
    def gen_cache_key(cls, subject_id):
        return f'aj_subject/{subject_id}/book/list/'

    @classmethod
    def fresh_cache(cls, subject_id):
        """
        刷新 当前 subject_id 的所有 book list 缓存
        """
        cache_key = cls.gen_cache_key(subject_id)

        cache.delete(cache_key)
