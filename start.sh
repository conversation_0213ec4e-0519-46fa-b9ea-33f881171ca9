#!/bin/zsh

export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion" # This loads nvm bash_completion


# 启动前端和后端
echo "———— starting frontend ————"
cd frontend || exit  # 进入前端目录
nvm use 18           # 切换到 Node.js 版本 18
yarn dev &           # 启动前端开发服务器

# 进入后端目录并启动 Django 服务器
echo "———— starting backend ————"
cd ../backend || exit  # 进入后端目录
source .venv/bin/activate  # 激活虚拟环境
python manage.py runserver localhost:12345  # 启动 Django 服务器

# 等待前端进程完成
wait