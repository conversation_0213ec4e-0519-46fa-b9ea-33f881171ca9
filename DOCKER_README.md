# Docker 部署说明

## 项目结构
```
my_steward_pro/
├── frontend/          # Vue3 前端项目
│   ├── Dockerfile     # 前端 Dockerfile
│   └── nginx.conf     # nginx 配置
├── backend/           # Django 后端项目
│   └── Dockerfile     # 后端 Dockerfile
├── docker-compose.yml # 服务编排文件
└── DOCKER_README.md   # 本文件
```

## 快速开始

### 1. 构建并启动所有服务
```bash
docker-compose up --build
```

### 2. 后台运行
```bash
docker-compose up -d --build
```

### 3. 停止服务
```bash
docker-compose down
```

### 4. 查看日志
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs frontend
docker-compose logs backend
```

## 访问地址

- **前端**: http://localhost:80
- **后端API**: http://localhost:8000

## 开发模式

如果您想在开发模式下运行，可以使用原来的 `start.sh` 脚本：

```bash
./start.sh
```

## 注意事项

1. **端口映射**:
   - 前端: 80 -> 80
   - 后端: 8000 -> 8000

2. **数据持久化**:
   - 后端媒体文件: `./backend/media:/app/media`
   - 后端静态文件: `./backend/static:/app/static`

3. **网络**:
   - 所有服务都在 `app-network` 网络中
   - 前端可以通过 `http://backend:8000` 访问后端

## 自定义配置

### 修改端口
在 `docker-compose.yml` 中修改 ports 配置：
```yaml
ports:
  - "3000:80"  # 将前端端口改为3000
```

### 添加数据库
取消注释 `docker-compose.yml` 中的数据库配置，并修改 Django 设置以使用 PostgreSQL。

### 环境变量
在 `docker-compose.yml` 的 environment 部分添加更多环境变量。 


## 其它

### 构建顺序总结


1. 读取 docker-compose.yml
   ↓
2. 构建后端镜像 (backend)
   ↓
3. 构建前端镜像 (frontend)
   ↓
4. 创建网络 (app-network)
   ↓
5. 启动后端容器
   ↓
6. 启动前端容器
   ↓
7. 服务就绪，可以访问


### `--build` 参数

docker-compose up --build 中的 --build 参数的意思是强制重新构建服务的镜像。

详细说明：
- 强制构建：即使 Dockerfile 或相关文件没有变化，使用 --build 参数也会重新构建镜像。这在您对 Dockerfile 或应用代码进行了修改，想确保新更改被应用时特别有用。

- 依赖更新：如果您在 docker-compose.yml 文件中添加了新的服务或更新了现有服务的配置，使用 --build 可以确保所有相关镜像都被重新构建

### 更新frontend，构建之后还是旧的代码 解决方案

- 原因：命名卷 frontend_dist 挂载到 volume，没有更新
- 解决：1.删除命名卷 2.单独 build frontend
  

```
# 查看
docker volume ls

# 删除卷
docker volume rm my_steward_pro_frontend_dist

# 重新构建
docker-compose up --build
```